#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统诊断工具
用于诊断核心系统加载问题
"""

import os
import sys
import traceback
from pathlib import Path

def check_python_environment():
    """检查Python环境"""
    print("=== Python环境检查 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python路径列表:")
    for path in sys.path:
        print(f"  - {path}")
    print()

def check_file_structure():
    """检查文件结构"""
    print("=== 文件结构检查 ===")
    
    required_files = [
        "game/script.rpy",
        "game/core/__init__.py",
        "game/core/database.py",
        "game/core/data_models.py",
        "game/core/world_generator.py",
        "game/core/npc_generator.py",
        "game/core/time_system.py",
        "game/core/behavior_system.py",
        "game/core/economy_system.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - 缺失")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 发现 {len(missing_files)} 个缺失文件")
        return False
    else:
        print("\n✅ 所有必需文件都存在")
        return True

def check_core_imports():
    """检查核心模块导入"""
    print("=== 核心模块导入检查 ===")
    
    # 添加game目录到Python路径
    game_path = os.path.join(os.getcwd(), 'game')
    if game_path not in sys.path:
        sys.path.insert(0, game_path)
    
    modules_to_test = [
        ('core', '核心包'),
        ('core.database', '数据库模块'),
        ('core.data_models', '数据模型'),
        ('core.world_generator', '世界生成器'),
        ('core.npc_generator', 'NPC生成器'),
        ('core.time_system', '时间系统'),
        ('core.behavior_system', '行为系统'),
        ('core.economy_system', '经济系统')
    ]
    
    failed_imports = []
    
    for module_name, display_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {display_name} ({module_name})")
        except Exception as e:
            print(f"✗ {display_name} ({module_name}) - 导入失败: {e}")
            failed_imports.append((module_name, str(e)))
    
    if failed_imports:
        print(f"\n❌ {len(failed_imports)} 个模块导入失败")
        return False, failed_imports
    else:
        print("\n✅ 所有核心模块导入成功")
        return True, []

def check_database_access():
    """检查数据库访问"""
    print("=== 数据库访问检查 ===")
    
    try:
        # 添加game目录到Python路径
        game_path = os.path.join(os.getcwd(), 'game')
        if game_path not in sys.path:
            sys.path.insert(0, game_path)
        
        from core.database import DatabaseManager
        
        # 尝试初始化数据库管理器
        db_manager = DatabaseManager()
        print("✓ 数据库管理器初始化成功")
        
        # 检查数据库文件
        db_path = os.path.join('game', 'saves', 'world.db')
        if os.path.exists(db_path):
            print(f"✓ 数据库文件存在: {db_path}")
        else:
            print(f"ℹ 数据库文件不存在，将在首次使用时创建: {db_path}")
        
        # 测试基本数据库操作
        worlds = db_manager.list_worlds()
        print(f"✓ 数据库查询成功，找到 {len(worlds)} 个世界")
        
        print("\n✅ 数据库访问正常")
        return True
        
    except Exception as e:
        print(f"✗ 数据库访问失败: {e}")
        print(f"详细错误信息:")
        traceback.print_exc()
        return False

def check_world_generation():
    """检查世界生成功能"""
    print("=== 世界生成功能检查 ===")
    
    try:
        # 添加game目录到Python路径
        game_path = os.path.join(os.getcwd(), 'game')
        if game_path not in sys.path:
            sys.path.insert(0, game_path)
        
        from core.database import DatabaseManager
        from core.world_generator import WorldGenerator
        
        db_manager = DatabaseManager()
        world_generator = WorldGenerator(db_manager)
        print("✓ 世界生成器初始化成功")
        
        print("\n✅ 世界生成功能正常")
        return True
        
    except Exception as e:
        print(f"✗ 世界生成功能失败: {e}")
        print(f"详细错误信息:")
        traceback.print_exc()
        return False

def main():
    """主诊断流程"""
    print("🔍 系统诊断工具")
    print("=" * 50)
    
    # 检查Python环境
    check_python_environment()
    
    # 检查文件结构
    files_ok = check_file_structure()
    if not files_ok:
        print("\n❌ 文件结构检查失败，请确保所有必需文件都存在")
        return
    
    # 检查核心模块导入
    imports_ok, failed_imports = check_core_imports()
    if not imports_ok:
        print("\n❌ 核心模块导入失败")
        print("失败的模块:")
        for module, error in failed_imports:
            print(f"  - {module}: {error}")
        return
    
    # 检查数据库访问
    db_ok = check_database_access()
    if not db_ok:
        print("\n❌ 数据库访问失败")
        return
    
    # 检查世界生成
    world_ok = check_world_generation()
    if not world_ok:
        print("\n❌ 世界生成功能失败")
        return
    
    print("\n" + "=" * 50)
    print("🎉 系统诊断完成！")
    print("✅ 所有检查都通过，系统应该可以正常运行")
    print("\n建议:")
    print("1. 尝试重新启动Ren'Py游戏")
    print("2. 如果仍有问题，请查看具体的错误信息")
    print("3. 确保Ren'Py版本兼容（推荐7.4+）")

if __name__ == "__main__":
    main()
