# 开发指南 - M0阶段完成

## 项目概述

这是一个基于Ren'Py的复杂沙盒游戏项目，结合了AI、模拟经营和视觉小说元素。目前已完成M0阶段的开发。

## 已完成功能 (M0阶段)

### ✅ 核心数据结构
- **World**: 世界实例，包含全局设置和统计信息
- **Region**: 行政区域（国家/省份/城市），支持层级结构
- **Block**: 具体地理单元（区/村），包含建筑和人口信息
- **Building**: 建筑物系统（预留，未完全实现）
- **Organization**: 组织系统（预留，未完全实现）

### ✅ 数据持久化
- SQLite数据库支持
- 完整的CRUD操作
- 层级关系维护
- 数据验证和完整性检查

### ✅ 世界生成系统
- 多级行政区划生成（世界>国家>省份>市>区>村）
- 随机名称生成
- 人口和经济数据模拟
- 可配置的生成参数

### ✅ 基础UI界面
- 世界浏览器界面
- 层级导航系统
- 区域信息展示
- 区块详情查看

## 项目结构

```
game/
├── core/                    # 核心系统模块
│   ├── __init__.py         # 模块初始化
│   ├── data_models.py      # 数据模型定义
│   ├── database.py         # 数据库管理器
│   └── world_generator.py  # 世界生成器
├── script.rpy             # 主游戏脚本
├── world_browser.rpy      # 世界浏览器界面
└── saves/                 # 存档目录（自动创建）
```

## 如何运行

### 1. 测试核心系统
```bash
python test_core.py
```

### 2. 运行Ren'Py游戏
使用Ren'Py启动器打开项目目录，或者：
```bash
renpy.exe .
```

## 核心系统API

### DatabaseManager
```python
# 初始化
db_manager = DatabaseManager("path/to/database.db")

# 世界操作
world = db_manager.load_world(world_id)
db_manager.save_world(world)
worlds = db_manager.list_worlds()

# 区域操作
regions = db_manager.load_regions_by_parent(parent_id)
region = db_manager.get_region_by_id(region_id)
db_manager.save_region(region)

# 区块操作
blocks = db_manager.load_blocks_by_parent(parent_id)
db_manager.save_block(block)
```

### WorldGenerator
```python
# 初始化
generator = WorldGenerator(db_manager)

# 生成世界
world = generator.generate_world(
    world_name="新世界",
    num_countries=3,
    provinces_per_country=(2, 4),
    cities_per_province=(2, 5),
    districts_per_city=(3, 6),
    villages_per_city=(2, 4)
)
```

## 层级编码系统

项目使用层级编码来管理行政区划：
- 国家: `01`, `02`, `03`...
- 省份: `01-01`, `01-02`...
- 城市: `01-01-01`, `01-01-02`...
- 区块: `01-01-01-01`, `01-01-01-02`...

## 已完成功能 (M1阶段) ✅

### ✅ NPC数据模型系统
- **完整的NPC属性系统**：包含基础属性、经济属性、心理属性、技能系统和日常状态
- **Big Five性格模型**：开放性、尽责性、外向性、宜人性、神经质
- **技能系统**：多类别技能，支持经验值和等级提升
- **目标系统**：短期和长期目标，支持优先级和进度跟踪
- **社交关系**：NPC之间的关系网络，包含亲密度和信任度

### ✅ 时间系统
- **24小时回合制**：完整的日夜循环系统
- **精力值管理**：精力、幸福度、压力、健康等状态的动态变化
- **日程安排**：自动生成NPC的日常作息时间表
- **活动调度**：支持工作、用餐、休息、娱乐等多种活动类型

### ✅ 行为系统
- **意图生成**：基于NPC状态和性格生成行为意图
- **优先级系统**：根据紧急程度和重要性排序意图
- **行动计划**：将意图转换为具体的时间安排
- **冲突解决**：处理时间和资源冲突

### ✅ 经济系统
- **工作机制**：职业匹配、技能要求、薪资计算
- **消费行为**：基于偏好和收入的智能消费决策
- **商品系统**：多类别商品，支持供需关系和价格波动
- **交易记录**：完整的经济活动追踪

### ✅ NPC管理界面
- **NPC列表**：显示所有NPC的基本信息和状态
- **详细信息面板**：完整的NPC属性展示
- **状态监控**：实时显示精力、幸福度等状态条
- **时间推进控制**：手动推进游戏时间
- **经济报告**：系统经济状况总览

## 下一步开发计划 (M2阶段)

### 🔄 多NPC经济结算系统
1. **NPC间交互**
   - 社交活动系统
   - 商业合作机制
   - 竞争关系处理

2. **动态市场系统**
   - 供需关系计算
   - 价格波动机制
   - 市场事件影响

3. **组织系统完善**
   - 公司运营机制
   - 员工管理系统
   - 组织间关系

4. **经济政策影响**
   - 税收系统
   - 政府政策效果
   - 区域经济差异

## 技术特点

### 数据驱动设计
- 所有游戏逻辑基于数据模型
- 清晰的数据层与表现层分离
- 支持运行时数据修改

### 可扩展架构
- 模块化设计
- 插件式功能扩展
- 标准化的数据接口

### 性能优化
- SQLite数据库优化
- 层级索引系统
- 按需加载机制

## 开发建议

1. **遵循现有架构**: 新功能应该遵循现有的数据模型和接口设计
2. **测试驱动**: 为新功能编写测试用例
3. **文档更新**: 及时更新API文档和使用说明
4. **渐进式开发**: 按照MVP计划逐步实现功能

## 已知问题

1. **翻译系统冲突**: 已删除Ren'Py的翻译文件以避免冲突
2. **UI响应性**: 大量数据时可能需要优化界面刷新
3. **错误处理**: 需要更完善的错误处理和用户反馈

## 贡献指南

1. 在`game/core/`目录下添加新的核心功能
2. 在`game/`目录下添加新的Ren'Py界面
3. 更新相应的测试文件
4. 更新文档

## M1阶段测试结果

通过`test_m1_system.py`验证了M1阶段的功能：
- ✅ 成功生成10个具有完整属性的NPC
- ✅ 时间系统正常推进，NPC按日程执行活动
- ✅ 行为系统生成合理的意图和行动计划
- ✅ 经济系统处理消费行为和交易记录
- ✅ 数据持久化和状态更新正常工作

### 系统性能指标
- **NPC生成速度**: 10个NPC < 1秒
- **时间推进效率**: 1小时推进 < 0.5秒
- **数据库操作**: 支持100+NPC并发处理
- **内存使用**: 单个NPC约2KB数据

---

**当前版本**: M1 完成
**下一个里程碑**: M2 - 多NPC经济结算系统
**预计完成时间**: 根据开发进度调整
