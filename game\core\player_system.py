# -*- coding: utf-8 -*-
"""
玩家角色系统
实现玩家角色切换、状态管理、体验模式等功能
"""

import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from .data_models import NPC, generate_uuid
from .database import DatabaseManager


class PlayerMode(Enum):
    """玩家模式"""
    GOD_MODE = "god_mode"          # 上帝模式（全知全能）
    CHARACTER_MODE = "character"   # 角色模式（扮演特定NPC）
    OBSERVER_MODE = "observer"     # 观察者模式（纯旁观）


class ViewPerspective(Enum):
    """视角类型"""
    FIRST_PERSON = "first_person"   # 第一人称
    THIRD_PERSON = "third_person"   # 第三人称
    OMNISCIENT = "omniscient"       # 全知视角


@dataclass
class PlayerState:
    """玩家状态"""
    player_id: str
    current_mode: PlayerMode
    current_character_id: Optional[str] = None
    perspective: ViewPerspective = ViewPerspective.THIRD_PERSON
    
    # 权限设置
    can_edit_world: bool = True
    can_control_npcs: bool = True
    can_modify_economy: bool = True
    can_generate_events: bool = True
    
    # 界面设置
    show_debug_info: bool = False
    show_npc_thoughts: bool = False
    show_hidden_stats: bool = False
    
    # 游戏进度
    play_time: float = 0.0
    actions_taken: int = 0
    characters_played: List[str] = field(default_factory=list)
    
    # 偏好设置
    auto_save_interval: int = 300  # 秒
    notification_level: str = "normal"  # minimal, normal, verbose
    
    created_at: str = ""
    last_active: str = ""


@dataclass
class CharacterExperience:
    """角色体验记录"""
    character_id: str
    character_name: str
    total_time_played: float = 0.0
    sessions: List[Dict[str, Any]] = field(default_factory=list)
    achievements: List[str] = field(default_factory=list)
    memorable_events: List[str] = field(default_factory=list)
    relationship_changes: Dict[str, float] = field(default_factory=dict)
    
    def add_session(self, duration: float, activities: List[str], outcomes: List[str]):
        """添加游戏会话记录"""
        session = {
            "start_time": datetime.now().isoformat(),
            "duration": duration,
            "activities": activities,
            "outcomes": outcomes
        }
        self.sessions.append(session)
        self.total_time_played += duration


class PlayerSystem:
    """玩家系统管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.current_player: Optional[PlayerState] = None
        self.character_experiences: Dict[str, CharacterExperience] = {}
        self.available_characters: List[NPC] = []
        
        # 初始化默认玩家
        self._initialize_default_player()
    
    def _initialize_default_player(self):
        """初始化默认玩家"""
        self.current_player = PlayerState(
            player_id=generate_uuid(),
            current_mode=PlayerMode.GOD_MODE,
            created_at=datetime.now().isoformat(),
            last_active=datetime.now().isoformat()
        )
    
    def switch_to_god_mode(self) -> bool:
        """切换到上帝模式"""
        if not self.current_player:
            return False
        
        # 保存当前角色状态
        if self.current_player.current_mode == PlayerMode.CHARACTER_MODE:
            self._save_character_session()
        
        self.current_player.current_mode = PlayerMode.GOD_MODE
        self.current_player.current_character_id = None
        self.current_player.perspective = ViewPerspective.OMNISCIENT
        self.current_player.last_active = datetime.now().isoformat()
        
        return True
    
    def switch_to_character_mode(self, character_id: str) -> bool:
        """切换到角色模式"""
        if not self.current_player:
            return False
        
        # 验证角色是否存在
        character = self.db.load_npc(character_id)
        if not character:
            return False
        
        # 保存当前状态
        if self.current_player.current_mode == PlayerMode.CHARACTER_MODE:
            self._save_character_session()
        
        # 切换到新角色
        self.current_player.current_mode = PlayerMode.CHARACTER_MODE
        self.current_player.current_character_id = character_id
        self.current_player.perspective = ViewPerspective.FIRST_PERSON
        self.current_player.last_active = datetime.now().isoformat()
        
        # 记录角色使用
        if character_id not in self.current_player.characters_played:
            self.current_player.characters_played.append(character_id)
        
        # 初始化角色体验记录
        if character_id not in self.character_experiences:
            self.character_experiences[character_id] = CharacterExperience(
                character_id=character_id,
                character_name=character.name
            )
        
        return True
    
    def switch_to_observer_mode(self) -> bool:
        """切换到观察者模式"""
        if not self.current_player:
            return False
        
        # 保存当前角色状态
        if self.current_player.current_mode == PlayerMode.CHARACTER_MODE:
            self._save_character_session()
        
        self.current_player.current_mode = PlayerMode.OBSERVER_MODE
        self.current_player.current_character_id = None
        self.current_player.perspective = ViewPerspective.THIRD_PERSON
        self.current_player.last_active = datetime.now().isoformat()
        
        return True
    
    def get_current_character(self) -> Optional[NPC]:
        """获取当前控制的角色"""
        if (self.current_player and 
            self.current_player.current_mode == PlayerMode.CHARACTER_MODE and
            self.current_player.current_character_id):
            return self.db.load_npc(self.current_player.current_character_id)
        return None
    
    def get_available_characters(self, refresh: bool = False) -> List[NPC]:
        """获取可用角色列表"""
        if refresh or not self.available_characters:
            self.available_characters = self.db.load_all_active_npcs()
        return self.available_characters
    
    def create_custom_character(self, character_data: Dict[str, Any]) -> Optional[str]:
        """创建自定义角色"""
        try:
            from .npc_generator import NPCGenerator
            npc_generator = NPCGenerator(self.db)
            
            # 创建NPC
            custom_npc = npc_generator.generate_npc()
            
            # 应用自定义数据
            if "name" in character_data:
                custom_npc.name = character_data["name"]
            if "age" in character_data:
                custom_npc.age = character_data["age"]
            if "gender" in character_data:
                from .data_models import Gender
                if isinstance(character_data["gender"], str):
                    custom_npc.gender = Gender(character_data["gender"])
                else:
                    custom_npc.gender = character_data["gender"]
            if "education_level" in character_data:
                from .data_models import EducationLevel
                if isinstance(character_data["education_level"], str):
                    custom_npc.education_level = EducationLevel(character_data["education_level"])
                else:
                    custom_npc.education_level = character_data["education_level"]
            
            # 保存到数据库
            self.db.save_npc(custom_npc)
            
            # 添加到可用角色列表
            self.available_characters.append(custom_npc)
            
            return custom_npc.id
            
        except Exception as e:
            print(f"创建自定义角色失败: {e}")
            return None
    
    def _save_character_session(self):
        """保存角色游戏会话"""
        if (not self.current_player or 
            not self.current_player.current_character_id or
            self.current_player.current_character_id not in self.character_experiences):
            return
        
        character_id = self.current_player.current_character_id
        experience = self.character_experiences[character_id]
        
        # 计算会话时长（简化处理）
        session_duration = 60.0  # 假设每次会话1分钟
        
        # 记录活动（这里可以根据实际情况扩展）
        activities = ["日常生活", "社交互动", "工作活动"]
        outcomes = ["状态变化", "关系发展"]
        
        experience.add_session(session_duration, activities, outcomes)
    
    def get_player_stats(self) -> Dict[str, Any]:
        """获取玩家统计信息"""
        if not self.current_player:
            return {}
        
        total_character_time = sum(
            exp.total_time_played for exp in self.character_experiences.values()
        )
        
        return {
            "current_mode": self.current_player.current_mode.value,
            "current_character": self.current_player.current_character_id,
            "perspective": self.current_player.perspective.value,
            "play_time": self.current_player.play_time,
            "actions_taken": self.current_player.actions_taken,
            "characters_played": len(self.current_player.characters_played),
            "total_character_time": total_character_time,
            "available_characters": len(self.available_characters)
        }
    
    def get_character_experience(self, character_id: str) -> Optional[CharacterExperience]:
        """获取角色体验记录"""
        return self.character_experiences.get(character_id)
    
    def update_player_preferences(self, preferences: Dict[str, Any]):
        """更新玩家偏好设置"""
        if not self.current_player:
            return
        
        if "show_debug_info" in preferences:
            self.current_player.show_debug_info = preferences["show_debug_info"]
        if "show_npc_thoughts" in preferences:
            self.current_player.show_npc_thoughts = preferences["show_npc_thoughts"]
        if "show_hidden_stats" in preferences:
            self.current_player.show_hidden_stats = preferences["show_hidden_stats"]
        if "auto_save_interval" in preferences:
            self.current_player.auto_save_interval = preferences["auto_save_interval"]
        if "notification_level" in preferences:
            self.current_player.notification_level = preferences["notification_level"]
    
    def can_perform_action(self, action: str) -> bool:
        """检查玩家是否可以执行特定操作"""
        if not self.current_player:
            return False
        
        # 上帝模式可以执行所有操作
        if self.current_player.current_mode == PlayerMode.GOD_MODE:
            return True
        
        # 观察者模式只能观察
        if self.current_player.current_mode == PlayerMode.OBSERVER_MODE:
            return action in ["view", "observe", "analyze"]
        
        # 角色模式根据权限检查
        if self.current_player.current_mode == PlayerMode.CHARACTER_MODE:
            character_actions = [
                "move", "interact", "work", "rest", "socialize", 
                "consume", "learn", "exercise"
            ]
            return action in character_actions
        
        return False
    
    def record_action(self, action: str, details: Dict[str, Any] = None):
        """记录玩家操作"""
        if not self.current_player:
            return
        
        self.current_player.actions_taken += 1
        self.current_player.last_active = datetime.now().isoformat()
        
        # 如果在角色模式下，记录到角色体验中
        if (self.current_player.current_mode == PlayerMode.CHARACTER_MODE and
            self.current_player.current_character_id):
            character_id = self.current_player.current_character_id
            if character_id in self.character_experiences:
                experience = self.character_experiences[character_id]
                # 这里可以记录更详细的行为数据
                if details and "memorable" in details and details["memorable"]:
                    experience.memorable_events.append(f"{action}: {details.get('description', '')}")
    
    def get_perspective_info(self) -> Dict[str, Any]:
        """获取当前视角信息"""
        if not self.current_player:
            return {}
        
        current_char = self.get_current_character()
        
        return {
            "mode": self.current_player.current_mode.value,
            "perspective": self.current_player.perspective.value,
            "character": {
                "id": current_char.id if current_char else None,
                "name": current_char.name if current_char else None,
                "location": current_char.current_location if current_char else None
            } if current_char else None,
            "permissions": {
                "can_edit_world": self.current_player.can_edit_world,
                "can_control_npcs": self.current_player.can_control_npcs,
                "can_modify_economy": self.current_player.can_modify_economy,
                "can_generate_events": self.current_player.can_generate_events
            },
            "ui_settings": {
                "show_debug_info": self.current_player.show_debug_info,
                "show_npc_thoughts": self.current_player.show_npc_thoughts,
                "show_hidden_stats": self.current_player.show_hidden_stats
            }
        }
