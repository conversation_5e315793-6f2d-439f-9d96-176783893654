#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试核心系统功能
"""

import sys
import os

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

from core import DatabaseManager, WorldGenerator, World

def test_core_system():
    """测试核心系统功能"""
    print("开始测试核心系统...")
    
    # 测试数据库管理器
    print("\n1. 测试数据库管理器...")
    db_manager = DatabaseManager("test_world.db")
    print("✓ 数据库管理器初始化成功")
    
    # 测试世界生成器
    print("\n2. 测试世界生成器...")
    world_generator = WorldGenerator(db_manager)
    print("✓ 世界生成器初始化成功")
    
    # 生成测试世界
    print("\n3. 生成测试世界...")
    world = world_generator.generate_world(
        world_name="测试世界",
        num_countries=2,
        provinces_per_country=(2, 2),
        cities_per_province=(2, 2),
        districts_per_city=(2, 3),
        villages_per_city=(1, 2)
    )
    print(f"✓ 世界生成成功: {world.name}")
    print(f"  - 总人口: {world.total_population:,}")
    print(f"  - 行政区域数: {world.total_regions}")
    print(f"  - 区块数: {world.total_blocks}")
    
    # 测试数据加载
    print("\n4. 测试数据加载...")
    loaded_world = db_manager.load_world(world.id)
    if loaded_world:
        print(f"✓ 世界加载成功: {loaded_world.name}")
    else:
        print("✗ 世界加载失败")
        return False
    
    # 测试区域查询
    print("\n5. 测试区域查询...")
    countries = db_manager.load_regions_by_parent(None)
    print(f"✓ 找到 {len(countries)} 个国家:")
    for country in countries:
        print(f"  - {country.name} (人口: {country.population:,})")
        
        provinces = db_manager.load_regions_by_parent(country.id)
        print(f"    └─ {len(provinces)} 个省份:")
        for province in provinces:
            print(f"      - {province.name} (人口: {province.population:,})")
            
            cities = db_manager.load_regions_by_parent(province.id)
            print(f"        └─ {len(cities)} 个城市:")
            for city in cities:
                print(f"          - {city.name} (人口: {city.population:,})")
                
                blocks = db_manager.load_blocks_by_parent(city.id)
                print(f"            └─ {len(blocks)} 个区块:")
                for block in blocks:
                    print(f"              - {block.name} ({block.type.value}, 人口: {block.population:,})")
    
    print("\n✓ 所有测试通过！核心系统工作正常。")
    return True

if __name__ == "__main__":
    try:
        success = test_core_system()
        if success:
            print("\n🎉 核心系统测试成功！可以开始开发UI界面了。")
        else:
            print("\n❌ 核心系统测试失败，请检查错误。")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
