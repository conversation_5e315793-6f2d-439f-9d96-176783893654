#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
M5阶段系统测试
测试AI演出层：TTS语音合成、文生图立绘、多媒体管理系统
"""

import sys
import os
import random
import time
from datetime import datetime

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

def test_m5_system():
    """测试M5阶段的AI演出层功能"""
    print("=" * 60)
    print("M5阶段系统测试：AI演出层 (TTS + 文生图 + 多媒体)")
    print("=" * 60)
    
    try:
        # 导入核心模块
        from core import DatabaseManager, WorldGenerator, NPCGenerator
        from core.tts_system import TTSManager, TTSRequest, VoiceProfile, VoiceGender, VoiceStyle
        from core.image_generation import ImageGenerationManager, ImageGenerationRequest, ImageStyle, ImageType
        from core.multimedia_manager import MultimediaManager, MediaType, MediaCategory
        
        print("✓ 所有核心模块导入成功")
        
        # 1. 初始化系统
        print("\n1. 初始化系统...")
        db_manager = DatabaseManager("test_m5.db")
        world_generator = WorldGenerator(db_manager)
        npc_generator = NPCGenerator(db_manager)
        
        # 初始化AI演出层组件
        tts_manager = TTSManager("test_audio")
        image_manager = ImageGenerationManager("test_images")
        multimedia_manager = MultimediaManager("test_media")
        
        print("✓ 系统初始化完成")
        
        # 2. 创建测试环境
        print("\n2. 创建测试环境...")
        world = world_generator.generate_world("M5测试世界")
        
        test_npcs = []
        for i in range(8):
            npc = npc_generator.generate_npc()
            npc.monthly_income = random.randint(2000, 8000)
            npc.cash = random.randint(500, 2000)
            test_npcs.append(npc)
            db_manager.save_npc(npc)
        
        print(f"✓ 创建了世界和 {len(test_npcs)} 个NPC")
        
        # 3. 测试TTS语音合成系统
        print("\n3. 测试TTS语音合成系统...")
        
        # 获取TTS服务状态
        tts_status = tts_manager.get_service_status()
        print(f"  TTS服务状态: {tts_status['default_provider']}")
        print(f"  可用语音数: {len(tts_manager.get_available_voices())}")
        
        # 为NPC生成语音
        test_npc = test_npcs[0]
        voice_profile = tts_manager.get_npc_voice_profile(test_npc)
        print(f"  为{test_npc.name}分配语音: {voice_profile.name} ({voice_profile.gender.value})")
        print(f"  语音参数: 语速{voice_profile.speed:.2f}, 音调{voice_profile.pitch:.2f}, 音量{voice_profile.volume:.2f}")
        
        # 测试语音合成
        test_texts = [
            f"你好，我是{test_npc.name}。",
            f"今天是个好天气，我感觉很不错。",
            f"我今年{test_npc.age}岁，很高兴认识你。"
        ]
        
        tts_results = []
        for i, text in enumerate(test_texts):
            print(f"  生成语音 {i+1}: {text[:20]}...")
            
            tts_request = TTSRequest(
                text=text,
                voice_profile=voice_profile,
                priority=3
            )
            
            start_time = time.time()
            result = tts_manager.synthesize_sync(tts_request)
            generation_time = time.time() - start_time
            
            if result.success:
                tts_results.append(result)
                print(f"    ✓ 成功生成，时长: {result.duration:.1f}秒, 文件大小: {result.file_size}字节")
                print(f"    生成耗时: {generation_time:.3f}秒")
            else:
                print(f"    ❌ 生成失败: {result.error_message}")
        
        print(f"✓ TTS测试完成，成功生成 {len(tts_results)} 个音频文件")
        
        # 4. 测试文生图立绘系统
        print("\n4. 测试文生图立绘系统...")
        
        # 获取图像生成服务状态
        image_status = image_manager.get_service_status()
        print(f"  图像生成服务状态: {image_status['default_provider']}")
        print(f"  支持的风格: {len(image_status['services']['mock']['supported_styles'])}")
        
        # 为不同NPC生成肖像
        image_results = []
        test_styles = [ImageStyle.ANIME, ImageStyle.REALISTIC, ImageStyle.CARTOON]
        
        for i, npc in enumerate(test_npcs[:3]):
            style = test_styles[i]
            print(f"  为{npc.name}生成{style.value}风格肖像...")
            
            # 构建提示词
            prompt = image_manager._build_npc_prompt(npc, style)
            print(f"    提示词: {prompt[:60]}...")
            
            image_request = ImageGenerationRequest(
                prompt=prompt,
                style=style,
                image_type=ImageType.PORTRAIT,
                width=512,
                height=512,
                priority=3
            )
            
            start_time = time.time()
            result = image_manager.generate_sync(image_request)
            generation_time = time.time() - start_time
            
            if result.success:
                image_results.append(result)
                print(f"    ✓ 成功生成，尺寸: {result.width}x{result.height}, 文件大小: {result.file_size}字节")
                print(f"    生成耗时: {generation_time:.3f}秒")
            else:
                print(f"    ❌ 生成失败: {result.error_message}")
        
        print(f"✓ 图像生成测试完成，成功生成 {len(image_results)} 个图像文件")
        
        # 5. 测试多媒体管理系统
        print("\n5. 测试多媒体管理系统...")
        
        # 获取多媒体系统状态
        multimedia_status = multimedia_manager.get_system_status()
        print(f"  多媒体系统状态:")
        print(f"    工作线程运行: {multimedia_status['worker_running']}")
        print(f"    待处理任务: {multimedia_status['pending_tasks']}")
        print(f"    缓存资源: {multimedia_status['cached_assets']}")
        
        # 启动多媒体工作线程
        multimedia_manager.start_worker()
        
        # 批量生成NPC媒体资源
        generation_tasks = []
        
        for npc in test_npcs[:4]:
            # 生成语音任务
            voice_task_id = multimedia_manager.generate_npc_voice(
                npc, 
                f"大家好，我是{npc.name}，很高兴在这里与大家见面。", 
                priority=2
            )
            generation_tasks.append(voice_task_id)
            
            # 生成肖像任务
            portrait_task_id = multimedia_manager.generate_npc_portrait(
                npc, 
                ImageStyle.ANIME, 
                priority=2
            )
            generation_tasks.append(portrait_task_id)
        
        print(f"  提交了 {len(generation_tasks)} 个生成任务")
        
        # 等待任务完成
        print("  等待任务完成...")
        completed_tasks = 0
        max_wait_time = 30  # 最多等待30秒
        start_wait = time.time()
        
        while completed_tasks < len(generation_tasks) and (time.time() - start_wait) < max_wait_time:
            for task_id in generation_tasks:
                task_status = multimedia_manager.get_task_status(task_id)
                if task_status and task_status.status in ["completed", "failed"]:
                    if task_id not in [t for t in generation_tasks if multimedia_manager.get_task_status(t).status in ["completed", "failed"]]:
                        completed_tasks += 1
                        if task_status.status == "completed":
                            print(f"    ✓ 任务完成: {task_status.media_type.value}")
                        else:
                            print(f"    ❌ 任务失败: {task_status.error_message}")
            
            time.sleep(0.5)
        
        print(f"✓ 多媒体任务处理完成，完成率: {completed_tasks}/{len(generation_tasks)}")
        
        # 6. 测试媒体资源查询
        print("\n6. 测试媒体资源查询...")
        
        for npc in test_npcs[:3]:
            # 获取NPC的所有媒体资源
            npc_assets = multimedia_manager.get_npc_assets(npc.id)
            print(f"  {npc.name} 的媒体资源: {len(npc_assets)} 个")
            
            for asset in npc_assets:
                print(f"    - {asset.media_type.value}: {asset.name} ({asset.file_size} 字节)")
            
            # 获取特定类型的资源
            audio_assets = multimedia_manager.get_npc_assets(npc.id, MediaType.AUDIO)
            image_assets = multimedia_manager.get_npc_assets(npc.id, MediaType.IMAGE)
            
            print(f"    音频资源: {len(audio_assets)} 个, 图像资源: {len(image_assets)} 个")
        
        # 7. 测试异步生成
        print("\n7. 测试异步生成...")
        
        async_results = []
        
        def async_callback(result):
            async_results.append(result)
            print(f"    异步回调: {result.request_id} - {'成功' if result.success else '失败'}")
        
        # 提交异步TTS请求
        test_npc = test_npcs[4]
        voice_profile = tts_manager.get_npc_voice_profile(test_npc)
        
        async_tts_request = TTSRequest(
            text=f"这是{test_npc.name}的异步语音测试。",
            voice_profile=voice_profile,
            priority=4
        )
        
        tts_manager.synthesize_async(async_tts_request, async_callback)
        
        # 提交异步图像请求
        async_image_request = ImageGenerationRequest(
            prompt=f"anime style portrait of {test_npc.name}",
            style=ImageStyle.ANIME,
            image_type=ImageType.PORTRAIT,
            priority=4
        )
        
        image_manager.generate_async(async_image_request, async_callback)
        
        # 等待异步结果
        wait_time = 0
        while len(async_results) < 2 and wait_time < 10:
            time.sleep(0.5)
            wait_time += 0.5
        
        print(f"✓ 异步生成测试完成，收到 {len(async_results)} 个回调")
        
        # 8. 性能测试
        print("\n8. 性能测试...")
        
        # 批量TTS性能测试
        batch_texts = [f"这是第{i+1}个测试文本。" for i in range(5)]
        
        start_time = time.time()
        batch_tts_results = []
        
        for text in batch_texts:
            request = TTSRequest(
                text=text,
                voice_profile=voice_profile,
                priority=1
            )
            result = tts_manager.synthesize_sync(request)
            if result.success:
                batch_tts_results.append(result)
        
        batch_time = time.time() - start_time
        
        print(f"  批量TTS生成: {len(batch_tts_results)}/{len(batch_texts)} 成功")
        print(f"  总耗时: {batch_time:.3f}秒")
        print(f"  平均每个: {batch_time/len(batch_texts):.3f}秒")
        
        # 批量图像生成性能测试
        batch_prompts = [f"anime character {i+1}" for i in range(3)]
        
        start_time = time.time()
        batch_image_results = []
        
        for prompt in batch_prompts:
            request = ImageGenerationRequest(
                prompt=prompt,
                style=ImageStyle.ANIME,
                image_type=ImageType.PORTRAIT,
                priority=1
            )
            result = image_manager.generate_sync(request)
            if result.success:
                batch_image_results.append(result)
        
        batch_time = time.time() - start_time
        
        print(f"  批量图像生成: {len(batch_image_results)}/{len(batch_prompts)} 成功")
        print(f"  总耗时: {batch_time:.3f}秒")
        print(f"  平均每个: {batch_time/len(batch_prompts):.3f}秒")
        
        # 9. 缓存测试
        print("\n9. 缓存测试...")
        
        # 测试TTS缓存
        cache_text = "这是缓存测试文本。"
        cache_request = TTSRequest(text=cache_text, voice_profile=voice_profile)
        
        # 第一次生成
        start_time = time.time()
        first_result = tts_manager.synthesize_sync(cache_request)
        first_time = time.time() - start_time
        
        # 第二次生成（应该使用缓存）
        start_time = time.time()
        second_result = tts_manager.synthesize_sync(cache_request)
        second_time = time.time() - start_time
        
        print(f"  TTS缓存测试:")
        print(f"    首次生成: {first_time:.3f}秒")
        print(f"    缓存命中: {second_time:.3f}秒")
        print(f"    加速比: {first_time/second_time:.1f}x" if second_time > 0 else "    加速比: ∞")
        
        # 10. 最终状态报告
        print("\n10. 最终系统状态...")
        
        final_tts_status = tts_manager.get_service_status()
        final_image_status = image_manager.get_service_status()
        final_multimedia_status = multimedia_manager.get_system_status()
        
        print("  TTS系统状态:")
        print(f"    工作线程: {'运行中' if final_tts_status['worker_running'] else '已停止'}")
        print(f"    队列大小: {final_tts_status['queue_size']}")
        print(f"    缓存大小: {final_tts_status['cache_size']}")
        print(f"    NPC语音配置: {final_tts_status['npc_voices']}")
        
        print("  图像生成系统状态:")
        print(f"    工作线程: {'运行中' if final_image_status['worker_running'] else '已停止'}")
        print(f"    队列大小: {final_image_status['queue_size']}")
        print(f"    缓存大小: {final_image_status['cache_size']}")
        print(f"    NPC肖像: {final_image_status['npc_portraits']}")
        
        print("  多媒体管理系统状态:")
        print(f"    工作线程: {'运行中' if final_multimedia_status['worker_running'] else '已停止'}")
        print(f"    待处理任务: {final_multimedia_status['pending_tasks']}")
        print(f"    总任务数: {final_multimedia_status['total_tasks']}")
        print(f"    缓存资源: {final_multimedia_status['cached_assets']}")
        
        # 清理资源
        multimedia_manager.stop_worker()
        
        print("\n" + "=" * 60)
        print("✅ M5阶段系统测试完成！")
        print("✅ TTS语音合成系统正常工作")
        print("✅ 文生图立绘系统正常工作")
        print("✅ 多媒体管理系统正常工作")
        print("✅ 异步处理机制正常工作")
        print("✅ 缓存机制正常工作")
        print("✅ 性能表现良好")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_m5_system()
    sys.exit(0 if success else 1)
