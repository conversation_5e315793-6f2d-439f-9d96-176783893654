# 手动设置指南

## 🎯 当前问题诊断

根据测试结果，系统中没有找到Ren'Py可执行文件。这是导致无法启动游戏的主要原因。

## 📥 Ren'Py安装步骤

### 方法1: 下载Ren'Py SDK（推荐）

1. **下载Ren'Py**
   - 访问 [Ren'Py官网](https://www.renpy.org/latest.html)
   - 下载最新版本的Ren'Py SDK
   - 推荐版本：Ren'Py 8.0+ 或 Ren'Py 7.4+

2. **解压安装**
   - 将下载的压缩包解压到任意目录，例如：
     - `C:\RenPy\`
     - `D:\Programs\RenPy\`
     - `C:\Program Files\RenPy\`

3. **验证安装**
   - 进入解压后的目录
   - 找到 `renpy.exe` 文件
   - 双击运行，应该会打开Ren'Py启动器

### 方法2: 使用便携版本

如果不想安装，可以下载便携版本直接使用。

## 🚀 启动游戏的方法

### 方法1: 使用Ren'Py启动器（最简单）

1. 运行 `renpy.exe`（在Ren'Py安装目录中）
2. 在启动器中点击"选择项目"或"Actions" → "选择项目"
3. 浏览到项目目录：`c:\gamelab\hf`
4. 点击"启动项目"

### 方法2: 命令行启动

1. 打开命令提示符（cmd）
2. 切换到项目目录：
   ```cmd
   cd c:\gamelab\hf
   ```
3. 运行Ren'Py（替换为您的实际Ren'Py路径）：
   ```cmd
   "C:\RenPy\renpy.exe" .
   ```

### 方法3: 修改启动脚本

编辑 `start_game.bat` 文件，在开头添加您的Ren'Py路径：

```batch
@echo off
set RENPY_PATH="C:\RenPy\renpy.exe"
echo 使用Ren'Py路径: %RENPY_PATH%
%RENPY_PATH% .
pause
```

## 🔧 项目状态验证

### 当前项目已经修复的问题：
- ✅ 所有Python核心模块正常工作
- ✅ 数据库系统正常
- ✅ 世界生成器正常
- ✅ NPC系统正常
- ✅ Ren'Py脚本语法正确

### 使用最小化测试版本

当前项目使用了最小化的测试脚本（`game/script.rpy`），它会：

1. **详细的启动诊断**
   - 显示每个模块的加载状态
   - 提供详细的错误信息
   - 逐步测试各个系统组件

2. **交互式测试**
   - 测试世界创建
   - 测试NPC生成
   - 测试时间系统

3. **错误处理**
   - 如果某个组件失败，会显示具体错误
   - 不会因为单个组件失败而崩溃

## 🧪 验证步骤

一旦您安装了Ren'Py，请按以下步骤验证：

### 步骤1: 基本启动测试
```cmd
cd c:\gamelab\hf
"您的Ren'Py路径\renpy.exe" .
```

### 步骤2: 观察启动信息
游戏启动后，您应该看到：
- "欢迎来到最小化测试版本！"
- 系统加载状态信息
- 如果成功，会显示"✅ 核心系统加载成功！"

### 步骤3: 测试功能
如果基本启动成功，您可以测试：
- 世界创建功能
- NPC生成功能
- 时间系统功能

## 🐛 常见问题解决

### Q: Ren'Py启动器打不开
A: 
1. 检查是否下载了完整的Ren'Py SDK
2. 确保解压完整
3. 尝试以管理员身份运行
4. 检查防病毒软件是否阻止

### Q: 选择项目后无法启动
A:
1. 确保选择的是包含 `game` 文件夹的目录
2. 检查项目路径中是否有中文字符
3. 尝试将项目复制到更简单的路径，如 `C:\game\`

### Q: 启动后立即关闭
A:
1. 查看Ren'Py启动器的日志
2. 检查是否有语法错误
3. 使用最小化测试版本进行诊断

### Q: 显示"核心系统加载失败"
A:
1. 这通常是Python环境问题
2. 确保使用的Ren'Py版本支持Python 3.8+
3. 检查项目文件是否完整

## 📞 获取帮助

如果按照以上步骤仍然无法启动，请提供以下信息：

1. **Ren'Py版本**：在Ren'Py启动器中查看版本号
2. **错误信息**：启动时显示的具体错误
3. **系统信息**：Windows版本
4. **安装路径**：Ren'Py和项目的完整路径

## 🎮 恢复完整版本

如果您想恢复原始的完整游戏脚本：

```cmd
cd c:\gamelab\hf
cp game/script_original.rpy game/script.rpy
```

但建议先用最小化版本确保基本功能正常。

---

**重要提示**：当前的主要问题是缺少Ren'Py环境，而不是代码问题。一旦安装了Ren'Py，游戏应该可以正常启动。
