# -*- coding: utf-8 -*-
# 世界浏览器界面

# 世界浏览器主屏幕
screen world_browser_screen():
    tag menu
    
    # 背景
    add "#2c3e50"
    
    # 标题栏
    frame:
        xalign 0.5
        ypos 20
        padding (20, 10)
        background "#34495e"
        
        text "[current_world.name] - 世界浏览器" size 30 color "#ecf0f1" xalign 0.5
    
    # 主要内容区域
    hbox:
        xalign 0.5
        yalign 0.5
        spacing 50
        
        # 左侧：世界信息面板
        frame:
            xsize 400
            ysize 500
            padding (20, 20)
            background "#34495e"
            
            vbox:
                spacing 15
                
                text "世界信息" size 24 color "#ecf0f1"
                
                null height 10
                
                text "名称: [current_world.name]" size 18 color "#bdc3c7"
                text "版本: [current_world.version]" size 18 color "#bdc3c7"
                text "当前日期: 第[current_world.current_day]天" size 18 color "#bdc3c7"
                text "季节: [current_world.current_season]" size 18 color "#bdc3c7"
                text "年份: [current_world.current_year]" size 18 color "#bdc3c7"
                
                null height 20
                
                text "统计信息" size 20 color "#ecf0f1"
                text "总人口: [current_world.total_population:,]" size 16 color "#bdc3c7"
                text "行政区域: [current_world.total_regions]" size 16 color "#bdc3c7"
                text "区块数量: [current_world.total_blocks]" size 16 color "#bdc3c7"
                
                null height 20
                
                text "经济信息" size 20 color "#ecf0f1"
                text "基础货币: [current_world.base_currency]" size 16 color "#bdc3c7"
                text "通胀率: [current_world.global_inflation_rate:.2%]" size 16 color "#bdc3c7"
                text "增长率: [current_world.global_growth_rate:.2%]" size 16 color "#bdc3c7"
        
        # 右侧：区域浏览面板
        frame:
            xsize 600
            ysize 500
            padding (20, 20)
            background "#34495e"
            
            vbox:
                spacing 15
                
                hbox:
                    text "区域浏览" size 24 color "#ecf0f1"
                    null width 20
                    if current_region:
                        textbutton "返回上级" action Function(navigate_up) text_size 16
                
                null height 10
                
                # 当前路径显示
                if current_region:
                    text "当前位置: [get_region_path()]" size 16 color "#95a5a6"
                else:
                    text "当前位置: 世界根目录" size 16 color "#95a5a6"
                
                null height 10
                
                # 区域列表
                viewport:
                    scrollbars "vertical"
                    mousewheel True
                    ysize 350
                    
                    vbox:
                        spacing 5
                        
                        python:
                            if current_region is None:
                                # 显示顶级国家
                                regions_to_show = db_manager.load_regions_by_parent(None)
                            else:
                                # 显示当前区域的子区域
                                regions_to_show = db_manager.load_regions_by_parent(current_region.id)
                                # 如果是城市级别，还要显示区块
                                if current_region.type.value == "city":
                                    current_blocks = db_manager.load_blocks_by_parent(current_region.id)
                                else:
                                    current_blocks = []
                        
                        # 显示子区域
                        for region in regions_to_show:
                            frame:
                                padding (10, 5)
                                background "#2c3e50"
                                xfill True
                                
                                hbox:
                                    spacing 10
                                    
                                    # 区域类型图标
                                    text get_region_icon(region.type.value) size 20
                                    
                                    vbox:
                                        spacing 2
                                        
                                        hbox:
                                            text "[region.name]" size 18 color "#ecf0f1"
                                            null width 10
                                            text "([region.type.value])" size 14 color "#95a5a6"
                                        
                                        text "人口: [region.population:,]" size 14 color "#bdc3c7"
                                        if hasattr(region, 'gdp') and region.gdp > 0:
                                            text "GDP: [region.gdp:,.0f]" size 14 color "#bdc3c7"
                                    
                                    null width 20
                                    
                                    textbutton "进入" action Function(navigate_to_region, region) text_size 14
                        
                        # 显示区块（如果当前在城市级别）
                        for block in current_blocks:
                            frame:
                                padding (10, 5)
                                background "#1a252f"
                                xfill True
                                
                                hbox:
                                    spacing 10
                                    
                                    # 区块类型图标
                                    text get_block_icon(block.type.value) size 20
                                    
                                    vbox:
                                        spacing 2
                                        
                                        hbox:
                                            text "[block.name]" size 18 color "#ecf0f1"
                                            null width 10
                                            text "([block.type.value])" size 14 color "#95a5a6"
                                        
                                        text "人口: [block.population:,]" size 14 color "#bdc3c7"
                                        text "面积: [block.land_area:.1f] km²" size 14 color "#bdc3c7"
                                        text "发展度: [block.development_level:.1f]" size 14 color "#bdc3c7"
                                    
                                    null width 20
                                    
                                    textbutton "详情" action Function(show_block_details, block) text_size 14
    
    # 底部按钮栏
    hbox:
        xalign 0.5
        ypos 650
        spacing 20

        textbutton "NPC管理" action Jump("npc_manager") text_size 16
        textbutton "新闻中心" action Jump("news_system") text_size 16
        textbutton "生成新世界" action Jump("world_creation") text_size 16
        textbutton "保存世界" action Function(save_current_world) text_size 16
        textbutton "退出游戏" action Quit() text_size 16

# 导航函数
init python:
    def navigate_to_region(region):
        global current_region
        current_region = region
        renpy.restart_interaction()
    
    def navigate_up():
        global current_region
        if current_region and current_region.parent_id:
            current_region = db_manager.get_region_by_id(current_region.parent_id)
        else:
            current_region = None
        renpy.restart_interaction()
    
    def get_region_path():
        if not current_region:
            return "世界"
        
        path_parts = []
        region = current_region
        while region:
            path_parts.append(region.name)
            if region.parent_id:
                region = db_manager.get_region_by_id(region.parent_id)
            else:
                break
        
        path_parts.reverse()
        return " > ".join(path_parts)
    
    def get_region_icon(region_type):
        icons = {
            "country": "🏛️",
            "province": "🏞️", 
            "city": "🏙️",
            "district": "🏢",
            "village": "🏘️"
        }
        return icons.get(region_type, "📍")
    
    def get_block_icon(block_type):
        icons = {
            "district": "🏢",
            "village": "🏘️"
        }
        return icons.get(block_type, "📍")
    
    def show_block_details(block):
        # 显示区块详细信息的对话框
        renpy.call_screen("block_details_screen", block=block)
    
    def save_current_world():
        if current_world:
            success = db_manager.save_world(current_world)
            if success:
                renpy.notify("世界保存成功！")
            else:
                renpy.notify("世界保存失败！")

# 区块详情屏幕
screen block_details_screen(block):
    modal True
    
    # 半透明背景
    add "#000000aa"
    
    # 详情面板
    frame:
        xalign 0.5
        yalign 0.5
        xsize 500
        padding (30, 30)
        background "#34495e"
        
        vbox:
            spacing 15
            
            text "[block.name] 详细信息" size 24 color "#ecf0f1" xalign 0.5
            
            null height 10
            
            text "基本信息" size 18 color "#ecf0f1"
            text "类型: [block.type.value]" size 16 color "#bdc3c7"
            text "所属: [block.parent_id]" size 16 color "#bdc3c7"
            text "面积: [block.land_area:.2f] 平方公里" size 16 color "#bdc3c7"
            
            null height 10
            
            text "人口与建设" size 18 color "#ecf0f1"
            text "当前人口: [block.population:,]" size 16 color "#bdc3c7"
            text "人口上限: [block.population_cap:,]" size 16 color "#bdc3c7"
            text "建筑容量: [block.buildable_index_used:.1f] / [block.buildable_index_max:.1f]" size 16 color "#bdc3c7"
            
            null height 10
            
            text "经济信息" size 18 color "#ecf0f1"
            text "地价: [block.land_price:,.0f] / m²" size 16 color "#bdc3c7"
            text "发展水平: [block.development_level:.2f]" size 16 color "#bdc3c7"
            text "基础设施: [block.infrastructure_score:.1f]" size 16 color "#bdc3c7"
            
            null height 20
            
            textbutton "关闭" action Return() xalign 0.5 text_size 16
