# -*- coding: utf-8 -*-
"""
经济系统
实现工作机制、消费行为和基础经济闭环
"""

import random
import math
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime
from .data_models import (
    NPC, Job, Organization, Building, JobType, OrganizationType, 
    generate_uuid
)
from .database import DatabaseManager


@dataclass
class JobOffer:
    """工作机会"""
    id: str
    title: str
    job_type: JobType
    organization_id: str
    building_id: str
    salary: float
    required_skills: List[str] = field(default_factory=list)
    required_education: str = "high_school"
    work_hours_per_day: int = 8
    is_available: bool = True
    created_at: str = ""


@dataclass
class Product:
    """商品"""
    id: str
    name: str
    category: str
    base_price: float
    quality: float = 50.0  # 质量 0-100
    producer_id: Optional[str] = None  # 生产者组织ID
    
    def get_market_price(self, demand: float, supply: float) -> float:
        """根据供需关系计算市场价格"""
        supply_demand_ratio = supply / max(demand, 1.0)
        price_multiplier = 1.0 / (1.0 + supply_demand_ratio)
        return self.base_price * (0.5 + price_multiplier)


@dataclass
class Transaction:
    """交易记录"""
    id: str
    buyer_id: str
    seller_id: str
    product_id: str
    quantity: int
    unit_price: float
    total_amount: float
    transaction_time: str
    location: str = ""


class EconomySystem:
    """经济系统管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.job_offers: Dict[str, JobOffer] = {}
        self.products: Dict[str, Product] = {}
        self.transactions: List[Transaction] = []
        
        # 基础商品类别
        self.product_categories = {
            "food": {"基础食物": 10, "精美餐食": 30, "特色小食": 20},
            "clothing": {"基础服装": 50, "时尚服装": 150, "奢侈服装": 500},
            "housing": {"基础住房": 1000, "舒适住房": 2000, "豪华住房": 5000},
            "entertainment": {"基础娱乐": 20, "高级娱乐": 80, "奢华娱乐": 200},
            "education": {"基础教育": 100, "专业培训": 500, "高等教育": 2000},
            "health": {"基础医疗": 50, "专业医疗": 200, "高端医疗": 800},
            "transport": {"公共交通": 5, "私人交通": 100, "豪华交通": 500},
            "technology": {"基础设备": 200, "先进设备": 800, "顶级设备": 2000}
        }
        
        # 职业薪资范围
        self.job_salary_ranges = {
            JobType.UNEMPLOYED: (0, 0),
            JobType.STUDENT: (0, 500),
            JobType.WORKER: (2000, 4000),
            JobType.CLERK: (3000, 5000),
            JobType.MANAGER: (5000, 10000),
            JobType.PROFESSIONAL: (6000, 12000),
            JobType.ENTREPRENEUR: (3000, 20000),
            JobType.GOVERNMENT: (4000, 8000),
            JobType.RETIRED: (1500, 3000)
        }
        
        self.initialize_products()
    
    def initialize_products(self):
        """初始化基础商品"""
        for category, items in self.product_categories.items():
            for item_name, base_price in items.items():
                product = Product(
                    id=generate_uuid(),
                    name=item_name,
                    category=category,
                    base_price=base_price,
                    quality=random.uniform(40, 80)
                )
                self.products[product.id] = product
    
    def create_job_offer(self, organization: Organization, building: Building, 
                        job_type: JobType, title: str, salary: float,
                        required_skills: List[str] = None) -> JobOffer:
        """创建工作机会"""
        if required_skills is None:
            required_skills = []
        
        job_offer = JobOffer(
            id=generate_uuid(),
            title=title,
            job_type=job_type,
            organization_id=organization.id,
            building_id=building.id,
            salary=salary,
            required_skills=required_skills,
            created_at=datetime.now().isoformat()
        )
        
        self.job_offers[job_offer.id] = job_offer
        return job_offer
    
    def find_suitable_jobs(self, npc: NPC, max_results: int = 5) -> List[JobOffer]:
        """为NPC寻找合适的工作"""
        suitable_jobs = []
        
        for job_offer in self.job_offers.values():
            if not job_offer.is_available:
                continue
            
            # 检查技能匹配度
            skill_match_score = self._calculate_skill_match(npc, job_offer)
            
            # 检查教育要求
            education_match = self._check_education_requirement(npc, job_offer)
            
            if education_match and skill_match_score > 0.3:  # 至少30%技能匹配
                suitable_jobs.append((job_offer, skill_match_score))
        
        # 按匹配度排序
        suitable_jobs.sort(key=lambda x: x[1], reverse=True)
        return [job for job, score in suitable_jobs[:max_results]]
    
    def apply_for_job(self, npc: NPC, job_offer: JobOffer) -> bool:
        """NPC申请工作"""
        if not job_offer.is_available:
            return False
        
        # 计算录取概率
        skill_match = self._calculate_skill_match(npc, job_offer)
        education_match = self._check_education_requirement(npc, job_offer)
        
        # 基础录取概率
        base_probability = 0.3
        skill_bonus = skill_match * 0.4
        education_bonus = 0.2 if education_match else 0
        
        # 性格影响
        personality_bonus = 0
        if job_offer.job_type in [JobType.MANAGER, JobType.PROFESSIONAL]:
            personality_bonus += (npc.personality.conscientiousness - 50) * 0.002
        
        total_probability = min(0.9, base_probability + skill_bonus + education_bonus + personality_bonus)
        
        if random.random() < total_probability:
            # 录取成功
            job = Job(
                id=generate_uuid(),
                title=job_offer.title,
                job_type=job_offer.job_type,
                organization_id=job_offer.organization_id,
                building_id=job_offer.building_id,
                salary=job_offer.salary,
                work_hours_per_day=job_offer.work_hours_per_day,
                required_skills=job_offer.required_skills.copy()
            )
            
            # 更新NPC工作状态
            if npc.current_job:
                npc.job_history.append(npc.current_job)
            
            npc.current_job = job
            npc.monthly_income = job.salary
            npc.work_location = job.building_id
            
            # 标记工作机会为已占用
            job_offer.is_available = False
            
            return True
        
        return False
    
    def _calculate_skill_match(self, npc: NPC, job_offer: JobOffer) -> float:
        """计算技能匹配度"""
        if not job_offer.required_skills:
            return 0.8  # 无技能要求的工作
        
        total_required = len(job_offer.required_skills)
        matched_skills = 0
        skill_level_sum = 0
        
        for required_skill in job_offer.required_skills:
            npc_skill_level = npc.get_skill_level(required_skill)
            if npc_skill_level > 20:  # 至少有基础水平
                matched_skills += 1
                skill_level_sum += npc_skill_level
        
        if matched_skills == 0:
            return 0.0
        
        match_ratio = matched_skills / total_required
        average_skill_level = skill_level_sum / matched_skills / 100.0  # 转换为0-1范围
        
        return match_ratio * 0.6 + average_skill_level * 0.4
    
    def _check_education_requirement(self, npc: NPC, job_offer: JobOffer) -> bool:
        """检查教育要求"""
        education_levels = {
            "none": 0, "primary": 1, "secondary": 2, "high_school": 3,
            "college": 4, "university": 5, "graduate": 6, "doctorate": 7
        }
        
        required_level = education_levels.get(job_offer.required_education, 3)
        npc_level = education_levels.get(npc.education_level.value, 0)
        
        return npc_level >= required_level
    
    def process_consumption(self, npc: NPC) -> List[Transaction]:
        """处理NPC消费行为"""
        transactions = []
        
        # 计算可支配收入
        disposable_income = max(0, npc.monthly_income - npc.monthly_expenses)
        
        if disposable_income <= 0:
            return transactions
        
        # 根据消费偏好分配支出
        for category, preference in npc.consumption_preferences.items():
            if preference < 30:  # 偏好太低，跳过
                continue
            
            # 计算该类别的预算
            budget_ratio = (preference / 100.0) * 0.3  # 最多30%的收入用于单一类别
            category_budget = disposable_income * budget_ratio
            
            if category_budget < 10:  # 预算太少，跳过
                continue
            
            # 寻找该类别的商品
            category_products = [p for p in self.products.values() if p.category == category]
            if not category_products:
                continue
            
            # 选择商品（考虑价格和质量）
            affordable_products = [p for p in category_products if p.base_price <= category_budget]
            if not affordable_products:
                continue
            
            # 根据NPC性格选择商品
            selected_product = self._select_product_by_personality(npc, affordable_products)
            
            if selected_product and npc.can_afford(selected_product.base_price):
                # 进行购买
                transaction = self._make_purchase(npc, selected_product, 1)
                if transaction:
                    transactions.append(transaction)
        
        return transactions
    
    def _select_product_by_personality(self, npc: NPC, products: List[Product]) -> Optional[Product]:
        """根据NPC性格选择商品"""
        if not products:
            return None
        
        # 根据性格特质调整选择倾向
        if npc.personality.conscientiousness > 60:
            # 尽责的人倾向于选择质量好的商品
            products.sort(key=lambda p: p.quality, reverse=True)
        elif npc.personality.neuroticism > 60:
            # 神经质的人倾向于选择便宜的商品
            products.sort(key=lambda p: p.base_price)
        else:
            # 其他情况随机选择
            random.shuffle(products)
        
        return products[0]
    
    def _make_purchase(self, npc: NPC, product: Product, quantity: int) -> Optional[Transaction]:
        """执行购买"""
        total_cost = product.base_price * quantity
        
        if not npc.can_afford(total_cost):
            return None
        
        # 扣除费用
        if npc.spend_money(total_cost, f"购买{product.name}"):
            # 创建交易记录
            transaction = Transaction(
                id=generate_uuid(),
                buyer_id=npc.id,
                seller_id="market",  # 简化处理，假设从市场购买
                product_id=product.id,
                quantity=quantity,
                unit_price=product.base_price,
                total_amount=total_cost,
                transaction_time=datetime.now().isoformat(),
                location=npc.current_location
            )
            
            self.transactions.append(transaction)
            
            # 更新NPC状态（购买带来的满足感）
            satisfaction = min(20, product.quality * 0.2)
            npc.update_daily_stats(happiness=satisfaction)
            
            return transaction
        
        return None
    
    def process_monthly_salary(self, npc: NPC):
        """处理月薪发放"""
        if npc.current_job and npc.current_job.is_active:
            # 基础工资
            base_salary = npc.current_job.salary
            
            # 技能奖金
            skill_bonus = 0
            for skill_name in npc.current_job.required_skills:
                skill_level = npc.get_skill_level(skill_name)
                skill_bonus += skill_level * 10  # 每级技能10元奖金
            
            # 性格奖金
            personality_bonus = 0
            if npc.personality.conscientiousness > 70:
                personality_bonus += base_salary * 0.1  # 10%奖金
            
            total_salary = base_salary + skill_bonus + personality_bonus
            
            # 发放工资
            npc.earn_money(total_salary, "月薪")
            
            # 工作技能提升
            for skill_name in npc.current_job.required_skills:
                npc.add_skill_experience(skill_name, 20.0)
    
    def get_economic_summary(self) -> Dict[str, Any]:
        """获取经济系统摘要"""
        active_npcs = self.db.load_all_active_npcs()
        
        total_cash = sum(npc.cash + npc.bank_balance for npc in active_npcs)
        total_income = sum(npc.monthly_income for npc in active_npcs)
        total_expenses = sum(npc.monthly_expenses for npc in active_npcs)
        
        employment_stats = {}
        for npc in active_npcs:
            job_type = npc.current_job.job_type.value if npc.current_job else "unemployed"
            employment_stats[job_type] = employment_stats.get(job_type, 0) + 1
        
        return {
            "total_npcs": len(active_npcs),
            "total_cash": total_cash,
            "total_monthly_income": total_income,
            "total_monthly_expenses": total_expenses,
            "employment_stats": employment_stats,
            "available_jobs": len([j for j in self.job_offers.values() if j.is_available]),
            "total_transactions": len(self.transactions),
            "products_available": len(self.products)
        }
    
    def generate_job_offers_for_organizations(self, organizations: List[Organization]):
        """为组织生成工作机会"""
        for org in organizations:
            if not org.is_active:
                continue
            
            # 根据组织类型生成不同的工作
            if org.type == OrganizationType.COMPANY:
                self._generate_company_jobs(org)
            elif org.type == OrganizationType.GOVERNMENT:
                self._generate_government_jobs(org)
            elif org.type == OrganizationType.SCHOOL:
                self._generate_school_jobs(org)
    
    def _generate_company_jobs(self, company: Organization):
        """为公司生成工作机会"""
        # 根据公司规模和资本生成不同级别的工作
        if company.capital > 1000000:  # 大公司
            job_types = [
                (JobType.MANAGER, "部门经理", 8000, ["管理", "领导"]),
                (JobType.PROFESSIONAL, "高级专员", 6000, ["专业技能"]),
                (JobType.CLERK, "办公文员", 4000, ["办公软件"]),
                (JobType.WORKER, "普通员工", 3000, [])
            ]
        else:  # 小公司
            job_types = [
                (JobType.CLERK, "办公助理", 3500, ["办公软件"]),
                (JobType.WORKER, "普通员工", 2800, [])
            ]
        
        for job_type, title, salary, skills in job_types:
            if len(company.employees) < company.max_employees:
                # 假设公司在第一个分支机构
                building_id = company.branches[0] if company.branches else ""
                self.create_job_offer(company, None, job_type, title, salary, skills)
    
    def _generate_government_jobs(self, gov_org: Organization):
        """为政府机构生成工作机会"""
        job_types = [
            (JobType.GOVERNMENT, "公务员", 5000, ["行政管理"]),
            (JobType.CLERK, "办事员", 3800, ["办公软件"])
        ]
        
        for job_type, title, salary, skills in job_types:
            if len(gov_org.employees) < gov_org.max_employees:
                building_id = gov_org.headquarters or (gov_org.branches[0] if gov_org.branches else "")
                self.create_job_offer(gov_org, None, job_type, title, salary, skills)
    
    def _generate_school_jobs(self, school: Organization):
        """为学校生成工作机会"""
        job_types = [
            (JobType.PROFESSIONAL, "教师", 5500, ["教学", "专业知识"]),
            (JobType.CLERK, "行政人员", 3200, ["办公软件"])
        ]
        
        for job_type, title, salary, skills in job_types:
            if len(school.employees) < school.max_employees:
                building_id = school.headquarters or (school.branches[0] if school.branches else "")
                self.create_job_offer(school, None, job_type, title, salary, skills)
