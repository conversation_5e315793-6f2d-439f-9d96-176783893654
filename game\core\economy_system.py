# -*- coding: utf-8 -*-
"""
经济系统
实现工作机制、消费行为和基础经济闭环
"""

import random
import math
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime
from .data_models import (
    NPC, Job, Organization, Building, JobType, OrganizationType, 
    generate_uuid
)
from .database import DatabaseManager


@dataclass
class JobOffer:
    """工作机会"""
    id: str
    title: str
    job_type: JobType
    organization_id: str
    building_id: str
    salary: float
    required_skills: List[str] = field(default_factory=list)
    required_education: str = "high_school"
    work_hours_per_day: int = 8
    is_available: bool = True
    created_at: str = ""


@dataclass
class MarketData:
    """市场数据"""
    product_id: str
    current_supply: float = 0.0
    current_demand: float = 0.0
    price_history: List[Tuple[str, float]] = field(default_factory=list)  # (timestamp, price)
    daily_transactions: int = 0
    weekly_trend: float = 0.0  # -1.0 to 1.0, 负值表示下降趋势
    seasonal_factor: float = 1.0  # 季节性因子
    regional_modifier: float = 1.0  # 地区修正因子

    def add_price_record(self, price: float, timestamp: str):
        """添加价格记录"""
        self.price_history.append((timestamp, price))
        # 保持最近30天的记录
        if len(self.price_history) > 30:
            self.price_history = self.price_history[-30:]

    def calculate_trend(self) -> float:
        """计算价格趋势"""
        if len(self.price_history) < 2:
            return 0.0

        recent_prices = [price for _, price in self.price_history[-7:]]  # 最近7天
        if len(recent_prices) < 2:
            return 0.0

        # 简单线性趋势计算
        avg_early = sum(recent_prices[:len(recent_prices)//2]) / (len(recent_prices)//2)
        avg_late = sum(recent_prices[len(recent_prices)//2:]) / (len(recent_prices) - len(recent_prices)//2)

        trend = (avg_late - avg_early) / avg_early if avg_early > 0 else 0.0
        return max(-1.0, min(1.0, trend))


@dataclass
class Product:
    """商品"""
    id: str
    name: str
    category: str
    base_price: float
    quality: float = 50.0  # 质量 0-100
    producer_id: Optional[str] = None  # 生产者组织ID
    durability: float = 100.0  # 耐久度 0-100
    rarity: float = 50.0  # 稀有度 0-100
    production_cost: float = 0.0  # 生产成本

    def get_market_price(self, market_data: MarketData, region_id: str = "", season: str = "") -> float:
        """根据市场数据计算动态价格"""
        # 基础供需价格
        supply_demand_ratio = market_data.current_supply / max(market_data.current_demand, 1.0)

        # 供需影响因子 (0.3 - 2.0)
        if supply_demand_ratio > 1.0:
            # 供过于求，价格下降
            supply_factor = max(0.3, 1.0 / (1.0 + (supply_demand_ratio - 1.0) * 2.0))
        else:
            # 供不应求，价格上升
            supply_factor = min(2.0, 1.0 + (1.0 - supply_demand_ratio) * 1.5)

        # 质量影响 (0.8 - 1.3)
        quality_factor = 0.8 + (self.quality / 100.0) * 0.5

        # 稀有度影响 (0.9 - 1.5)
        rarity_factor = 0.9 + (self.rarity / 100.0) * 0.6

        # 趋势影响 (0.9 - 1.1)
        trend_factor = 1.0 + market_data.weekly_trend * 0.1

        # 季节性影响
        seasonal_factor = market_data.seasonal_factor

        # 地区影响
        regional_factor = market_data.regional_modifier

        # 计算最终价格
        final_price = (self.base_price * supply_factor * quality_factor *
                      rarity_factor * trend_factor * seasonal_factor * regional_factor)

        # 确保价格不低于生产成本的80%
        min_price = max(self.production_cost * 0.8, self.base_price * 0.2)

        return max(min_price, final_price)

    def get_quality_adjusted_price(self, base_market_price: float) -> float:
        """根据质量调整价格"""
        quality_multiplier = 0.7 + (self.quality / 100.0) * 0.6
        return base_market_price * quality_multiplier


@dataclass
class Transaction:
    """交易记录"""
    id: str
    buyer_id: str
    seller_id: str
    product_id: str
    quantity: int
    unit_price: float
    total_amount: float
    transaction_time: str
    location: str = ""


class EconomySystem:
    """经济系统管理器"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.job_offers: Dict[str, JobOffer] = {}
        self.products: Dict[str, Product] = {}
        self.market_data: Dict[str, MarketData] = {}  # product_id -> MarketData
        self.transactions: List[Transaction] = []
        self.daily_transaction_count = 0
        self.current_season = "spring"  # spring, summer, autumn, winter

        # 基础商品类别 - 扩展版本
        self.product_categories = {
            "food": {
                "基础食物": {"price": 10, "cost": 6, "quality": 40, "rarity": 10},
                "精美餐食": {"price": 30, "cost": 18, "quality": 70, "rarity": 40},
                "特色小食": {"price": 20, "cost": 12, "quality": 60, "rarity": 30},
                "有机食品": {"price": 25, "cost": 15, "quality": 80, "rarity": 60},
                "进口食品": {"price": 45, "cost": 30, "quality": 75, "rarity": 70}
            },
            "clothing": {
                "基础服装": {"price": 50, "cost": 25, "quality": 45, "rarity": 15},
                "时尚服装": {"price": 150, "cost": 80, "quality": 70, "rarity": 50},
                "奢侈服装": {"price": 500, "cost": 250, "quality": 90, "rarity": 85},
                "运动服装": {"price": 80, "cost": 40, "quality": 65, "rarity": 35},
                "工作服装": {"price": 120, "cost": 60, "quality": 75, "rarity": 25}
            },
            "housing": {
                "基础住房": {"price": 1000, "cost": 600, "quality": 50, "rarity": 20},
                "舒适住房": {"price": 2000, "cost": 1200, "quality": 70, "rarity": 40},
                "豪华住房": {"price": 5000, "cost": 3000, "quality": 90, "rarity": 80},
                "学生公寓": {"price": 800, "cost": 500, "quality": 45, "rarity": 30},
                "商务公寓": {"price": 3000, "cost": 1800, "quality": 80, "rarity": 60}
            },
            "entertainment": {
                "基础娱乐": {"price": 20, "cost": 10, "quality": 50, "rarity": 20},
                "高级娱乐": {"price": 80, "cost": 40, "quality": 75, "rarity": 50},
                "奢华娱乐": {"price": 200, "cost": 120, "quality": 90, "rarity": 75},
                "户外活动": {"price": 35, "cost": 20, "quality": 65, "rarity": 30},
                "文化活动": {"price": 60, "cost": 35, "quality": 80, "rarity": 55}
            },
            "education": {
                "基础教育": {"price": 100, "cost": 50, "quality": 60, "rarity": 25},
                "专业培训": {"price": 500, "cost": 300, "quality": 80, "rarity": 60},
                "高等教育": {"price": 2000, "cost": 1200, "quality": 90, "rarity": 70},
                "在线课程": {"price": 80, "cost": 30, "quality": 65, "rarity": 40},
                "技能认证": {"price": 300, "cost": 150, "quality": 75, "rarity": 50}
            },
            "health": {
                "基础医疗": {"price": 50, "cost": 25, "quality": 60, "rarity": 30},
                "专业医疗": {"price": 200, "cost": 120, "quality": 80, "rarity": 60},
                "高端医疗": {"price": 800, "cost": 480, "quality": 95, "rarity": 85},
                "预防保健": {"price": 80, "cost": 40, "quality": 70, "rarity": 40},
                "康复治疗": {"price": 150, "cost": 90, "quality": 75, "rarity": 55}
            },
            "transport": {
                "公共交通": {"price": 5, "cost": 2, "quality": 40, "rarity": 10},
                "私人交通": {"price": 100, "cost": 60, "quality": 70, "rarity": 45},
                "豪华交通": {"price": 500, "cost": 300, "quality": 90, "rarity": 80},
                "共享出行": {"price": 15, "cost": 8, "quality": 55, "rarity": 25},
                "长途运输": {"price": 200, "cost": 120, "quality": 65, "rarity": 50}
            },
            "technology": {
                "基础设备": {"price": 200, "cost": 120, "quality": 50, "rarity": 30},
                "先进设备": {"price": 800, "cost": 480, "quality": 80, "rarity": 65},
                "顶级设备": {"price": 2000, "cost": 1200, "quality": 95, "rarity": 85},
                "智能设备": {"price": 600, "cost": 350, "quality": 85, "rarity": 70},
                "专业工具": {"price": 400, "cost": 240, "quality": 75, "rarity": 55}
            }
        }

        # 季节性影响因子
        self.seasonal_factors = {
            "food": {"spring": 1.0, "summer": 0.9, "autumn": 1.1, "winter": 1.2},
            "clothing": {"spring": 1.1, "summer": 0.8, "autumn": 1.3, "winter": 1.4},
            "housing": {"spring": 1.1, "summer": 1.0, "autumn": 0.9, "winter": 0.8},
            "entertainment": {"spring": 1.2, "summer": 1.3, "autumn": 1.0, "winter": 0.8},
            "education": {"spring": 1.3, "summer": 0.8, "autumn": 1.2, "winter": 1.0},
            "health": {"spring": 1.0, "summer": 0.9, "autumn": 1.1, "winter": 1.3},
            "transport": {"spring": 1.1, "summer": 1.2, "autumn": 1.0, "winter": 0.9},
            "technology": {"spring": 1.0, "summer": 1.0, "autumn": 1.1, "winter": 1.2}
        }

        # 职业薪资范围
        self.job_salary_ranges = {
            JobType.UNEMPLOYED: (0, 0),
            JobType.STUDENT: (0, 500),
            JobType.WORKER: (2000, 4000),
            JobType.CLERK: (3000, 5000),
            JobType.MANAGER: (5000, 10000),
            JobType.PROFESSIONAL: (6000, 12000),
            JobType.ENTREPRENEUR: (3000, 20000),
            JobType.GOVERNMENT: (4000, 8000),
            JobType.RETIRED: (1500, 3000)
        }

        self.initialize_products()
        self.initialize_market_data()
    
    def initialize_products(self):
        """初始化基础商品"""
        for category, items in self.product_categories.items():
            for item_name, item_data in items.items():
                product = Product(
                    id=generate_uuid(),
                    name=item_name,
                    category=category,
                    base_price=item_data["price"],
                    quality=item_data["quality"] + random.uniform(-10, 10),
                    rarity=item_data["rarity"] + random.uniform(-5, 5),
                    production_cost=item_data["cost"]
                )
                # 确保数值在合理范围内
                product.quality = max(10, min(100, product.quality))
                product.rarity = max(5, min(100, product.rarity))

                self.products[product.id] = product

    def initialize_market_data(self):
        """初始化市场数据"""
        from datetime import datetime
        current_time = datetime.now().isoformat()

        for product_id, product in self.products.items():
            # 初始供需量基于商品稀有度
            base_supply = max(10, 100 - product.rarity)
            base_demand = max(5, 80 - product.rarity * 0.5)

            # 添加随机波动
            initial_supply = base_supply * random.uniform(0.7, 1.3)
            initial_demand = base_demand * random.uniform(0.8, 1.2)

            market_data = MarketData(
                product_id=product_id,
                current_supply=initial_supply,
                current_demand=initial_demand,
                seasonal_factor=self.seasonal_factors.get(product.category, {}).get(self.current_season, 1.0),
                regional_modifier=random.uniform(0.9, 1.1)
            )

            # 添加初始价格记录
            initial_price = product.get_market_price(market_data)
            market_data.add_price_record(initial_price, current_time)

            self.market_data[product_id] = market_data
    
    def create_job_offer(self, organization: Organization, building: Building, 
                        job_type: JobType, title: str, salary: float,
                        required_skills: List[str] = None) -> JobOffer:
        """创建工作机会"""
        if required_skills is None:
            required_skills = []
        
        job_offer = JobOffer(
            id=generate_uuid(),
            title=title,
            job_type=job_type,
            organization_id=organization.id,
            building_id=building.id,
            salary=salary,
            required_skills=required_skills,
            created_at=datetime.now().isoformat()
        )
        
        self.job_offers[job_offer.id] = job_offer
        return job_offer
    
    def find_suitable_jobs(self, npc: NPC, max_results: int = 5) -> List[JobOffer]:
        """为NPC寻找合适的工作"""
        suitable_jobs = []
        
        for job_offer in self.job_offers.values():
            if not job_offer.is_available:
                continue
            
            # 检查技能匹配度
            skill_match_score = self._calculate_skill_match(npc, job_offer)
            
            # 检查教育要求
            education_match = self._check_education_requirement(npc, job_offer)
            
            if education_match and skill_match_score > 0.3:  # 至少30%技能匹配
                suitable_jobs.append((job_offer, skill_match_score))
        
        # 按匹配度排序
        suitable_jobs.sort(key=lambda x: x[1], reverse=True)
        return [job for job, score in suitable_jobs[:max_results]]
    
    def apply_for_job(self, npc: NPC, job_offer: JobOffer) -> bool:
        """NPC申请工作"""
        if not job_offer.is_available:
            return False
        
        # 计算录取概率
        skill_match = self._calculate_skill_match(npc, job_offer)
        education_match = self._check_education_requirement(npc, job_offer)
        
        # 基础录取概率
        base_probability = 0.3
        skill_bonus = skill_match * 0.4
        education_bonus = 0.2 if education_match else 0
        
        # 性格影响
        personality_bonus = 0
        if job_offer.job_type in [JobType.MANAGER, JobType.PROFESSIONAL]:
            personality_bonus += (npc.personality.conscientiousness - 50) * 0.002
        
        total_probability = min(0.9, base_probability + skill_bonus + education_bonus + personality_bonus)
        
        if random.random() < total_probability:
            # 录取成功
            job = Job(
                id=generate_uuid(),
                title=job_offer.title,
                job_type=job_offer.job_type,
                organization_id=job_offer.organization_id,
                building_id=job_offer.building_id,
                salary=job_offer.salary,
                work_hours_per_day=job_offer.work_hours_per_day,
                required_skills=job_offer.required_skills.copy()
            )
            
            # 更新NPC工作状态
            if npc.current_job:
                npc.job_history.append(npc.current_job)
            
            npc.current_job = job
            npc.monthly_income = job.salary
            npc.work_location = job.building_id
            
            # 标记工作机会为已占用
            job_offer.is_available = False
            
            return True
        
        return False
    
    def _calculate_skill_match(self, npc: NPC, job_offer: JobOffer) -> float:
        """计算技能匹配度"""
        if not job_offer.required_skills:
            return 0.8  # 无技能要求的工作
        
        total_required = len(job_offer.required_skills)
        matched_skills = 0
        skill_level_sum = 0
        
        for required_skill in job_offer.required_skills:
            npc_skill_level = npc.get_skill_level(required_skill)
            if npc_skill_level > 20:  # 至少有基础水平
                matched_skills += 1
                skill_level_sum += npc_skill_level
        
        if matched_skills == 0:
            return 0.0
        
        match_ratio = matched_skills / total_required
        average_skill_level = skill_level_sum / matched_skills / 100.0  # 转换为0-1范围
        
        return match_ratio * 0.6 + average_skill_level * 0.4
    
    def _check_education_requirement(self, npc: NPC, job_offer: JobOffer) -> bool:
        """检查教育要求"""
        education_levels = {
            "none": 0, "primary": 1, "secondary": 2, "high_school": 3,
            "college": 4, "university": 5, "graduate": 6, "doctorate": 7
        }
        
        required_level = education_levels.get(job_offer.required_education, 3)
        npc_level = education_levels.get(npc.education_level.value, 0)
        
        return npc_level >= required_level
    
    def process_consumption(self, npc: NPC) -> List[Transaction]:
        """处理NPC消费行为 - 使用动态价格"""
        transactions = []

        # 计算可支配收入
        disposable_income = max(0, npc.monthly_income - npc.monthly_expenses)

        if disposable_income <= 0:
            return transactions

        # 根据消费偏好分配支出
        for category, preference in npc.consumption_preferences.items():
            if preference < 30:  # 偏好太低，跳过
                continue

            # 计算该类别的预算
            budget_ratio = (preference / 100.0) * 0.3  # 最多30%的收入用于单一类别
            category_budget = disposable_income * budget_ratio

            if category_budget < 10:  # 预算太少，跳过
                continue

            # 寻找该类别的商品
            category_products = [p for p in self.products.values() if p.category == category]
            if not category_products:
                continue

            # 计算动态价格并筛选可负担商品
            affordable_products = []
            for product in category_products:
                market_data = self.market_data.get(product.id)
                if market_data:
                    current_price = product.get_market_price(market_data, npc.current_location, self.current_season)
                    if current_price <= category_budget:
                        affordable_products.append((product, current_price))

            if not affordable_products:
                continue

            # 根据NPC性格选择商品
            selected_item = self._select_product_by_personality_with_price(npc, affordable_products)

            if selected_item:
                product, current_price = selected_item
                if npc.can_afford(current_price):
                    # 进行购买
                    transaction = self._make_purchase_with_dynamic_price(npc, product, current_price, 1)
                    if transaction:
                        transactions.append(transaction)
                        # 更新市场数据
                        self._update_market_after_purchase(product.id, 1, current_price)

        return transactions
    
    def _select_product_by_personality(self, npc: NPC, products: List[Product]) -> Optional[Product]:
        """根据NPC性格选择商品（旧版本，保持兼容性）"""
        if not products:
            return None

        # 根据性格特质调整选择倾向
        if npc.personality.conscientiousness > 60:
            # 尽责的人倾向于选择质量好的商品
            products.sort(key=lambda p: p.quality, reverse=True)
        elif npc.personality.neuroticism > 60:
            # 神经质的人倾向于选择便宜的商品
            products.sort(key=lambda p: p.base_price)
        else:
            # 其他情况随机选择
            random.shuffle(products)

        return products[0]

    def _select_product_by_personality_with_price(self, npc: NPC, product_price_pairs: List[Tuple[Product, float]]) -> Optional[Tuple[Product, float]]:
        """根据NPC性格选择商品（考虑动态价格）"""
        if not product_price_pairs:
            return None

        # 根据性格特质调整选择倾向
        if npc.personality.conscientiousness > 60:
            # 尽责的人倾向于选择质量好的商品
            product_price_pairs.sort(key=lambda x: x[0].quality, reverse=True)
        elif npc.personality.neuroticism > 60:
            # 神经质的人倾向于选择便宜的商品
            product_price_pairs.sort(key=lambda x: x[1])
        elif npc.personality.openness > 60:
            # 开放的人倾向于尝试稀有商品
            product_price_pairs.sort(key=lambda x: x[0].rarity, reverse=True)
        elif npc.personality.extraversion > 60:
            # 外向的人倾向于选择高质量商品（展示用）
            product_price_pairs.sort(key=lambda x: x[0].quality * x[0].rarity, reverse=True)
        else:
            # 其他情况随机选择
            random.shuffle(product_price_pairs)

        return product_price_pairs[0]
    
    def _make_purchase(self, npc: NPC, product: Product, quantity: int) -> Optional[Transaction]:
        """执行购买（旧版本，保持兼容性）"""
        total_cost = product.base_price * quantity

        if not npc.can_afford(total_cost):
            return None

        # 扣除费用
        if npc.spend_money(total_cost, f"购买{product.name}"):
            # 创建交易记录
            transaction = Transaction(
                id=generate_uuid(),
                buyer_id=npc.id,
                seller_id="market",  # 简化处理，假设从市场购买
                product_id=product.id,
                quantity=quantity,
                unit_price=product.base_price,
                total_amount=total_cost,
                transaction_time=datetime.now().isoformat(),
                location=npc.current_location
            )

            self.transactions.append(transaction)

            # 更新NPC状态（购买带来的满足感）
            satisfaction = min(20, product.quality * 0.2)
            npc.update_daily_stats(happiness=satisfaction)

            return transaction

        return None

    def _make_purchase_with_dynamic_price(self, npc: NPC, product: Product, current_price: float, quantity: int) -> Optional[Transaction]:
        """执行购买（使用动态价格）"""
        total_cost = current_price * quantity

        if not npc.can_afford(total_cost):
            return None

        # 扣除费用
        if npc.spend_money(total_cost, f"购买{product.name}"):
            # 创建交易记录
            transaction = Transaction(
                id=generate_uuid(),
                buyer_id=npc.id,
                seller_id="market",  # 简化处理，假设从市场购买
                product_id=product.id,
                quantity=quantity,
                unit_price=current_price,
                total_amount=total_cost,
                transaction_time=datetime.now().isoformat(),
                location=npc.current_location
            )

            self.transactions.append(transaction)
            self.daily_transaction_count += 1

            # 更新NPC状态（购买带来的满足感）
            # 考虑价格合理性对满足感的影响
            price_satisfaction_factor = min(1.5, product.base_price / max(current_price, 1.0))
            satisfaction = min(25, product.quality * 0.2 * price_satisfaction_factor)
            npc.update_daily_stats(happiness=satisfaction)

            return transaction

        return None

    def _update_market_after_purchase(self, product_id: str, quantity: int, price: float):
        """购买后更新市场数据"""
        market_data = self.market_data.get(product_id)
        if not market_data:
            return

        # 减少供应量
        market_data.current_supply = max(0, market_data.current_supply - quantity)

        # 增加需求（购买行为表明需求）
        market_data.current_demand += quantity * 0.1

        # 更新交易计数
        market_data.daily_transactions += quantity

        # 记录价格
        from datetime import datetime
        market_data.add_price_record(price, datetime.now().isoformat())

        # 更新趋势
        market_data.weekly_trend = market_data.calculate_trend()

    def update_market_supply(self, product_id: str, supply_change: float, reason: str = ""):
        """更新商品供应量"""
        market_data = self.market_data.get(product_id)
        if market_data:
            market_data.current_supply = max(0, market_data.current_supply + supply_change)

    def update_market_demand(self, product_id: str, demand_change: float, reason: str = ""):
        """更新商品需求量"""
        market_data = self.market_data.get(product_id)
        if market_data:
            market_data.current_demand = max(0, market_data.current_demand + demand_change)

    def simulate_market_fluctuations(self):
        """模拟市场波动"""
        for product_id, market_data in self.market_data.items():
            # 自然供需波动
            supply_change = random.uniform(-5, 5)
            demand_change = random.uniform(-3, 3)

            market_data.current_supply = max(5, market_data.current_supply + supply_change)
            market_data.current_demand = max(1, market_data.current_demand + demand_change)

            # 重置每日交易计数
            market_data.daily_transactions = 0

    def update_seasonal_factors(self, new_season: str):
        """更新季节性因子"""
        if new_season != self.current_season:
            self.current_season = new_season

            for product_id, market_data in self.market_data.items():
                product = self.products.get(product_id)
                if product:
                    category_factors = self.seasonal_factors.get(product.category, {})
                    market_data.seasonal_factor = category_factors.get(new_season, 1.0)

    def process_monthly_salary(self, npc: NPC):
        """处理月薪发放"""
        if npc.current_job and npc.current_job.is_active:
            # 基础工资
            base_salary = npc.current_job.salary
            
            # 技能奖金
            skill_bonus = 0
            for skill_name in npc.current_job.required_skills:
                skill_level = npc.get_skill_level(skill_name)
                skill_bonus += skill_level * 10  # 每级技能10元奖金
            
            # 性格奖金
            personality_bonus = 0
            if npc.personality.conscientiousness > 70:
                personality_bonus += base_salary * 0.1  # 10%奖金
            
            total_salary = base_salary + skill_bonus + personality_bonus
            
            # 发放工资
            npc.earn_money(total_salary, "月薪")
            
            # 工作技能提升
            for skill_name in npc.current_job.required_skills:
                npc.add_skill_experience(skill_name, 20.0)
    
    def get_economic_summary(self) -> Dict[str, Any]:
        """获取经济系统摘要 - 增强版"""
        active_npcs = self.db.load_all_active_npcs()

        total_cash = sum(npc.cash + npc.bank_balance for npc in active_npcs)
        total_income = sum(npc.monthly_income for npc in active_npcs)
        total_expenses = sum(npc.monthly_expenses for npc in active_npcs)

        employment_stats = {}
        for npc in active_npcs:
            job_type = npc.current_job.job_type.value if npc.current_job else "unemployed"
            employment_stats[job_type] = employment_stats.get(job_type, 0) + 1

        # 计算市场统计
        total_supply = sum(md.current_supply for md in self.market_data.values())
        total_demand = sum(md.current_demand for md in self.market_data.values())
        avg_price_change = 0
        price_change_count = 0

        for product_id, market_data in self.market_data.items():
            product = self.products.get(product_id)
            if product and len(market_data.price_history) > 0:
                current_price = product.get_market_price(market_data)
                price_change = ((current_price - product.base_price) / product.base_price * 100) if product.base_price > 0 else 0
                avg_price_change += price_change
                price_change_count += 1

        if price_change_count > 0:
            avg_price_change /= price_change_count

        return {
            "total_npcs": len(active_npcs),
            "total_cash": total_cash,
            "total_monthly_income": total_income,
            "total_monthly_expenses": total_expenses,
            "employment_stats": employment_stats,
            "available_jobs": len([j for j in self.job_offers.values() if j.is_available]),
            "total_transactions": len(self.transactions),
            "daily_transactions": self.daily_transaction_count,
            "products_available": len(self.products),
            "market_supply": total_supply,
            "market_demand": total_demand,
            "supply_demand_ratio": total_supply / max(total_demand, 1.0),
            "avg_price_change": avg_price_change,
            "current_season": self.current_season,
            "market_activity": "活跃" if self.daily_transaction_count > 10 else "平静"
        }
    
    def generate_job_offers_for_organizations(self, organizations: List[Organization]):
        """为组织生成工作机会"""
        for org in organizations:
            if not org.is_active:
                continue
            
            # 根据组织类型生成不同的工作
            if org.type == OrganizationType.COMPANY:
                self._generate_company_jobs(org)
            elif org.type == OrganizationType.GOVERNMENT:
                self._generate_government_jobs(org)
            elif org.type == OrganizationType.SCHOOL:
                self._generate_school_jobs(org)
    
    def _generate_company_jobs(self, company: Organization):
        """为公司生成工作机会"""
        # 根据公司规模和资本生成不同级别的工作
        if company.capital > 1000000:  # 大公司
            job_types = [
                (JobType.MANAGER, "部门经理", 8000, ["管理", "领导"]),
                (JobType.PROFESSIONAL, "高级专员", 6000, ["专业技能"]),
                (JobType.CLERK, "办公文员", 4000, ["办公软件"]),
                (JobType.WORKER, "普通员工", 3000, [])
            ]
        else:  # 小公司
            job_types = [
                (JobType.CLERK, "办公助理", 3500, ["办公软件"]),
                (JobType.WORKER, "普通员工", 2800, [])
            ]
        
        for job_type, title, salary, skills in job_types:
            if len(company.employees) < company.max_employees:
                # 假设公司在第一个分支机构
                building_id = company.branches[0] if company.branches else ""
                self.create_job_offer(company, None, job_type, title, salary, skills)
    
    def _generate_government_jobs(self, gov_org: Organization):
        """为政府机构生成工作机会"""
        job_types = [
            (JobType.GOVERNMENT, "公务员", 5000, ["行政管理"]),
            (JobType.CLERK, "办事员", 3800, ["办公软件"])
        ]
        
        for job_type, title, salary, skills in job_types:
            if len(gov_org.employees) < gov_org.max_employees:
                building_id = gov_org.headquarters or (gov_org.branches[0] if gov_org.branches else "")
                self.create_job_offer(gov_org, None, job_type, title, salary, skills)
    
    def _generate_school_jobs(self, school: Organization):
        """为学校生成工作机会"""
        job_types = [
            (JobType.PROFESSIONAL, "教师", 5500, ["教学", "专业知识"]),
            (JobType.CLERK, "行政人员", 3200, ["办公软件"])
        ]
        
        for job_type, title, salary, skills in job_types:
            if len(school.employees) < school.max_employees:
                building_id = school.headquarters or (school.branches[0] if school.branches else "")
                self.create_job_offer(school, None, job_type, title, salary, skills)

    def get_market_analysis(self) -> Dict[str, Any]:
        """获取市场分析报告"""
        analysis = {
            "total_products": len(self.products),
            "total_transactions_today": self.daily_transaction_count,
            "current_season": self.current_season,
            "category_analysis": {},
            "price_trends": {},
            "supply_demand_status": {}
        }

        # 按类别分析
        category_stats = {}
        for product in self.products.values():
            category = product.category
            if category not in category_stats:
                category_stats[category] = {
                    "count": 0,
                    "avg_price": 0,
                    "total_supply": 0,
                    "total_demand": 0,
                    "transactions": 0
                }

            market_data = self.market_data.get(product.id)
            if market_data:
                current_price = product.get_market_price(market_data)
                category_stats[category]["count"] += 1
                category_stats[category]["avg_price"] += current_price
                category_stats[category]["total_supply"] += market_data.current_supply
                category_stats[category]["total_demand"] += market_data.current_demand
                category_stats[category]["transactions"] += market_data.daily_transactions

        # 计算平均值
        for category, stats in category_stats.items():
            if stats["count"] > 0:
                stats["avg_price"] /= stats["count"]
                analysis["category_analysis"][category] = stats

        # 价格趋势分析
        for product_id, market_data in self.market_data.items():
            product = self.products.get(product_id)
            if product and len(market_data.price_history) > 1:
                recent_prices = [price for _, price in market_data.price_history[-7:]]
                if len(recent_prices) >= 2:
                    trend = "上升" if recent_prices[-1] > recent_prices[0] else "下降"
                    analysis["price_trends"][product.name] = {
                        "trend": trend,
                        "current_price": recent_prices[-1],
                        "change_rate": ((recent_prices[-1] - recent_prices[0]) / recent_prices[0] * 100) if recent_prices[0] > 0 else 0
                    }

        return analysis

    def get_product_market_info(self, product_id: str) -> Optional[Dict[str, Any]]:
        """获取特定商品的市场信息"""
        product = self.products.get(product_id)
        market_data = self.market_data.get(product_id)

        if not product or not market_data:
            return None

        current_price = product.get_market_price(market_data)

        return {
            "product_name": product.name,
            "category": product.category,
            "base_price": product.base_price,
            "current_price": current_price,
            "price_change": ((current_price - product.base_price) / product.base_price * 100) if product.base_price > 0 else 0,
            "supply": market_data.current_supply,
            "demand": market_data.current_demand,
            "supply_demand_ratio": market_data.current_supply / max(market_data.current_demand, 1.0),
            "daily_transactions": market_data.daily_transactions,
            "weekly_trend": market_data.weekly_trend,
            "seasonal_factor": market_data.seasonal_factor,
            "quality": product.quality,
            "rarity": product.rarity
        }
