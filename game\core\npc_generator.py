# -*- coding: utf-8 -*-
"""
NPC生成器
负责生成具有完整属性的NPC角色
"""

import random
import math
from typing import List, Dict, Tuple
from datetime import datetime, timedelta
from .data_models import (
    NPC, Job, Skill, PersonalityTrait, DailyStats, Goal, NPCRelationship,
    Gender, EducationLevel, JobType, generate_uuid
)
from .database import DatabaseManager


class NPCGenerator:
    """NPC生成器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        
        # 姓名库
        self.first_names_male = [
            "伟", "强", "明", "华", "建", "军", "杰", "涛", "磊", "超",
            "勇", "峰", "鹏", "辉", "斌", "龙", "飞", "刚", "东", "波"
        ]
        self.first_names_female = [
            "丽", "娜", "敏", "静", "秀", "美", "芳", "燕", "红", "霞",
            "玲", "萍", "莉", "婷", "雪", "梅", "洁", "琳", "欣", "蕾"
        ]
        self.last_names = [
            "王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴",
            "徐", "孙", "胡", "朱", "高", "林", "何", "郭", "马", "罗"
        ]
        
        # 技能库
        self.skill_categories = {
            "professional": ["编程", "设计", "会计", "销售", "管理", "教学", "医疗", "法律"],
            "social": ["沟通", "领导", "谈判", "演讲", "团队合作", "客户服务"],
            "creative": ["绘画", "音乐", "写作", "摄影", "手工", "烹饪"],
            "physical": ["运动", "驾驶", "修理", "建筑", "园艺"],
            "intellectual": ["数学", "科学", "历史", "语言", "逻辑", "研究"]
        }
        
        # 价值观库
        self.value_types = [
            "family", "career", "money", "freedom", "security", "adventure",
            "knowledge", "creativity", "helping_others", "recognition"
        ]
    
    def generate_npc(self, 
                    age_range: Tuple[int, int] = (18, 65),
                    gender: Gender = None,
                    education_level: EducationLevel = None,
                    residence_building_id: str = "",
                    job_type: JobType = None) -> NPC:
        """
        生成单个NPC
        
        Args:
            age_range: 年龄范围
            gender: 指定性别（None为随机）
            education_level: 指定教育水平（None为随机）
            residence_building_id: 居住建筑ID
            job_type: 指定职业类型（None为随机）
        """
        # 基础属性
        age = random.randint(*age_range)
        if gender is None:
            gender = random.choice(list(Gender))
        
        name = self._generate_name(gender)
        
        if education_level is None:
            education_level = self._generate_education_level(age)
        
        # 生成出生日期
        birth_date = self._generate_birth_date(age)
        
        # 生成性格
        personality = self._generate_personality()
        
        # 生成技能
        skills = self._generate_skills(age, education_level)
        
        # 生成价值观
        values = self._generate_values(personality)
        
        # 生成目标
        goals = self._generate_goals(age, personality)
        
        # 生成经济状况
        cash, bank_balance, monthly_income, monthly_expenses = self._generate_economic_status(
            age, education_level, job_type
        )
        
        # 生成消费偏好
        consumption_preferences = self._generate_consumption_preferences(personality, values)
        
        # 创建NPC实例
        npc = NPC(
            id=generate_uuid(),
            name=name,
            age=age,
            gender=gender,
            education_level=education_level,
            birth_date=birth_date,
            birth_place="",  # 将在后续设置
            current_residence=residence_building_id,
            cash=cash,
            bank_balance=bank_balance,
            monthly_income=monthly_income,
            monthly_expenses=monthly_expenses,
            personality=personality,
            values=values,
            goals=goals,
            skills=skills,
            daily_stats=DailyStats(),
            consumption_preferences=consumption_preferences,
            home_location=residence_building_id,
            current_location=residence_building_id,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        return npc
    
    def generate_npcs_for_block(self, block_id: str, count: int) -> List[NPC]:
        """为特定区块生成NPC"""
        npcs = []
        
        # 获取区块信息
        block = self.db.get_block_by_id(block_id)  # 需要实现这个方法
        if not block:
            return npcs
        
        for _ in range(count):
            # 根据区块类型调整NPC生成参数
            if block.type.value == "district":
                age_range = (18, 65)
                education_weights = {
                    EducationLevel.HIGH_SCHOOL: 0.3,
                    EducationLevel.COLLEGE: 0.4,
                    EducationLevel.UNIVERSITY: 0.2,
                    EducationLevel.GRADUATE: 0.1
                }
            else:  # village
                age_range = (16, 70)
                education_weights = {
                    EducationLevel.PRIMARY: 0.2,
                    EducationLevel.SECONDARY: 0.3,
                    EducationLevel.HIGH_SCHOOL: 0.3,
                    EducationLevel.COLLEGE: 0.2
                }
            
            education_level = random.choices(
                list(education_weights.keys()),
                weights=list(education_weights.values())
            )[0]
            
            npc = self.generate_npc(
                age_range=age_range,
                education_level=education_level,
                residence_building_id=""  # 需要分配具体建筑
            )
            
            npc.birth_place = block_id
            npcs.append(npc)
        
        return npcs
    
    def _generate_name(self, gender: Gender) -> str:
        """生成姓名"""
        last_name = random.choice(self.last_names)
        if gender == Gender.MALE:
            first_name = random.choice(self.first_names_male)
        else:
            first_name = random.choice(self.first_names_female)
        return last_name + first_name
    
    def _generate_birth_date(self, age: int) -> str:
        """生成出生日期"""
        current_year = datetime.now().year
        birth_year = current_year - age
        birth_month = random.randint(1, 12)
        birth_day = random.randint(1, 28)  # 简化处理，避免月份天数问题
        return f"{birth_year}-{birth_month:02d}-{birth_day:02d}"
    
    def _generate_education_level(self, age: int) -> EducationLevel:
        """根据年龄生成教育水平"""
        if age < 16:
            return EducationLevel.PRIMARY
        elif age < 18:
            return EducationLevel.SECONDARY
        else:
            # 成年人的教育水平分布
            weights = [0.1, 0.15, 0.3, 0.25, 0.15, 0.04, 0.01]
            levels = [
                EducationLevel.PRIMARY, EducationLevel.SECONDARY,
                EducationLevel.HIGH_SCHOOL, EducationLevel.COLLEGE,
                EducationLevel.UNIVERSITY, EducationLevel.GRADUATE,
                EducationLevel.DOCTORATE
            ]
            return random.choices(levels, weights=weights)[0]
    
    def _generate_personality(self) -> PersonalityTrait:
        """生成性格特质"""
        return PersonalityTrait(
            openness=random.gauss(50, 15),
            conscientiousness=random.gauss(50, 15),
            extraversion=random.gauss(50, 15),
            agreeableness=random.gauss(50, 15),
            neuroticism=random.gauss(50, 15)
        )
    
    def _generate_skills(self, age: int, education_level: EducationLevel) -> Dict[str, Skill]:
        """生成技能"""
        skills = {}
        
        # 基础技能数量根据年龄和教育水平
        base_skills = min(3 + (age - 18) // 5, 10)
        education_bonus = {
            EducationLevel.PRIMARY: 0,
            EducationLevel.SECONDARY: 1,
            EducationLevel.HIGH_SCHOOL: 2,
            EducationLevel.COLLEGE: 3,
            EducationLevel.UNIVERSITY: 4,
            EducationLevel.GRADUATE: 5,
            EducationLevel.DOCTORATE: 6
        }
        
        total_skills = base_skills + education_bonus.get(education_level, 0)
        
        # 随机选择技能
        all_skills = []
        for category, skill_list in self.skill_categories.items():
            all_skills.extend(skill_list)
        
        selected_skills = random.sample(all_skills, min(total_skills, len(all_skills)))
        
        for skill_name in selected_skills:
            # 技能等级基于年龄和教育水平
            base_level = random.uniform(10, 30)
            age_bonus = (age - 18) * 0.5
            education_bonus_level = education_bonus.get(education_level, 0) * 5
            
            level = min(100, base_level + age_bonus + education_bonus_level + random.uniform(-10, 10))
            skills[skill_name] = Skill(skill_name, max(0, level))
        
        return skills
    
    def _generate_values(self, personality: PersonalityTrait) -> Dict[str, float]:
        """根据性格生成价值观"""
        values = {}
        
        for value_type in self.value_types:
            # 基础值
            base_value = random.uniform(30, 70)
            
            # 根据性格调整
            if value_type == "family" and personality.agreeableness > 60:
                base_value += 20
            elif value_type == "career" and personality.conscientiousness > 60:
                base_value += 20
            elif value_type == "adventure" and personality.openness > 60:
                base_value += 20
            elif value_type == "security" and personality.neuroticism > 60:
                base_value += 20
            
            values[value_type] = min(100, max(0, base_value))
        
        return values
    
    def _generate_goals(self, age: int, personality: PersonalityTrait) -> List[Goal]:
        """生成目标"""
        goals = []
        
        # 根据年龄阶段生成不同目标
        if age < 25:
            goal_types = ["完成学业", "找到工作", "建立社交圈"]
        elif age < 35:
            goal_types = ["职业发展", "买房", "结婚"]
        elif age < 50:
            goal_types = ["升职", "子女教育", "投资理财"]
        else:
            goal_types = ["健康保养", "退休准备", "家庭和谐"]
        
        for i, goal_type in enumerate(goal_types[:2]):  # 最多2个目标
            goal = Goal(
                id=generate_uuid(),
                description=goal_type,
                type="long_term" if i == 0 else "short_term",
                priority=random.uniform(60, 90),
                progress=random.uniform(0, 30)
            )
            goals.append(goal)
        
        return goals
    
    def _generate_economic_status(self, age: int, education_level: EducationLevel, job_type: JobType) -> Tuple[float, float, float, float]:
        """生成经济状况"""
        # 基础收入根据教育水平
        education_income_multiplier = {
            EducationLevel.PRIMARY: 0.6,
            EducationLevel.SECONDARY: 0.7,
            EducationLevel.HIGH_SCHOOL: 0.8,
            EducationLevel.COLLEGE: 1.0,
            EducationLevel.UNIVERSITY: 1.3,
            EducationLevel.GRADUATE: 1.6,
            EducationLevel.DOCTORATE: 2.0
        }
        
        base_income = 3000 * education_income_multiplier.get(education_level, 1.0)
        age_bonus = (age - 25) * 100 if age > 25 else 0
        monthly_income = base_income + age_bonus + random.uniform(-500, 1000)
        monthly_income = max(1000, monthly_income)  # 最低收入
        
        # 支出约为收入的70-90%
        monthly_expenses = monthly_income * random.uniform(0.7, 0.9)
        
        # 现金和存款
        cash = random.uniform(500, 5000)
        bank_balance = monthly_income * random.uniform(2, 12)  # 2-12个月的收入
        
        return cash, bank_balance, monthly_income, monthly_expenses
    
    def _generate_consumption_preferences(self, personality: PersonalityTrait, values: Dict[str, float]) -> Dict[str, float]:
        """生成消费偏好"""
        preferences = {
            "food": 50.0,
            "housing": 50.0,
            "clothing": 50.0,
            "entertainment": 50.0,
            "education": 50.0,
            "health": 50.0,
            "travel": 50.0,
            "technology": 50.0
        }
        
        # 根据性格和价值观调整偏好
        if personality.openness > 60:
            preferences["travel"] += 20
            preferences["education"] += 15
        
        if personality.conscientiousness > 60:
            preferences["health"] += 20
            preferences["education"] += 15
        
        if values.get("family", 50) > 70:
            preferences["housing"] += 20
            preferences["food"] += 15
        
        # 确保值在0-100范围内
        for key in preferences:
            preferences[key] = min(100, max(0, preferences[key]))
        
        return preferences
