#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试Ren'Py启动
模拟Ren'Py环境并测试脚本
"""

import os
import sys
import subprocess
import time

def find_renpy():
    """查找Ren'Py可执行文件"""
    possible_paths = [
        "renpy.exe",
        "renpy",
        r"C:\Program Files\RenPy\renpy.exe",
        r"C:\Program Files (x86)\RenPy\renpy.exe",
        r"C:\RenPy\renpy.exe",
        r"D:\RenPy\renpy.exe"
    ]
    
    for path in possible_paths:
        try:
            # 测试是否可以执行
            result = subprocess.run([path, "--version"], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=10)
            if result.returncode == 0:
                print(f"找到Ren'Py: {path}")
                return path
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            continue
    
    return None

def test_renpy_syntax():
    """测试Ren'Py语法"""
    print("=== 测试Ren'Py语法 ===")
    
    renpy_path = find_renpy()
    if not renpy_path:
        print("❌ 找不到Ren'Py可执行文件")
        return False
    
    try:
        # 使用Ren'Py的语法检查功能
        result = subprocess.run([renpy_path, ".", "--lint"], 
                              capture_output=True, 
                              text=True, 
                              timeout=30,
                              cwd=os.getcwd())
        
        print(f"Ren'Py语法检查返回码: {result.returncode}")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        # 返回码0表示没有语法错误
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Ren'Py语法检查超时")
        return False
    except Exception as e:
        print(f"❌ Ren'Py语法检查失败: {e}")
        return False

def test_renpy_launch():
    """测试Ren'Py启动"""
    print("\n=== 测试Ren'Py启动 ===")
    
    renpy_path = find_renpy()
    if not renpy_path:
        print("❌ 找不到Ren'Py可执行文件")
        return False
    
    print("尝试启动Ren'Py（将在5秒后自动关闭）...")
    
    try:
        # 启动Ren'Py，但设置超时
        process = subprocess.Popen([renpy_path, "."], 
                                 cwd=os.getcwd(),
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        # 等待5秒
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ Ren'Py启动成功（进程正在运行）")
            process.terminate()
            process.wait(timeout=5)
            return True
        else:
            print("❌ Ren'Py启动失败或立即退出")
            stdout, stderr = process.communicate()
            if stdout:
                print("标准输出:")
                print(stdout)
            if stderr:
                print("错误输出:")
                print(stderr)
            return False
            
    except Exception as e:
        print(f"❌ Ren'Py启动测试失败: {e}")
        return False

def check_project_structure():
    """检查项目结构"""
    print("=== 检查项目结构 ===")
    
    required_files = [
        "game/script.rpy",
        "game/options.rpy",
        "game/gui.rpy",
        "game/screens.rpy"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} - 缺失")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def main():
    """主测试流程"""
    print("🧪 Ren'Py直接测试")
    print("=" * 50)
    
    print(f"当前目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    print()
    
    # 检查项目结构
    structure_ok = check_project_structure()
    if not structure_ok:
        print("\n❌ 项目结构不完整")
        return
    
    # 测试语法
    syntax_ok = test_renpy_syntax()
    if not syntax_ok:
        print("\n❌ Ren'Py语法检查失败")
        print("请检查脚本文件中的语法错误")
        return
    
    print("\n✅ Ren'Py语法检查通过")
    
    # 测试启动
    launch_ok = test_renpy_launch()
    if launch_ok:
        print("\n🎉 Ren'Py可以正常启动！")
        print("\n现在您可以:")
        print("1. 双击 start_game.bat 启动游戏")
        print("2. 使用Ren'Py启动器选择项目目录")
        print("3. 运行命令: renpy.exe .")
    else:
        print("\n❌ Ren'Py启动测试失败")
        print("请检查:")
        print("1. Ren'Py是否正确安装")
        print("2. 项目文件是否完整")
        print("3. 是否有权限问题")

if __name__ == "__main__":
    main()
