#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试脚本 - 避免Unicode编码问题
"""

import os
import sys

def test_file_structure():
    """检查文件结构"""
    print("=== 文件结构检查 ===")
    
    required_files = [
        "game/script.rpy",
        "game/core/__init__.py",
        "game/core/database.py",
        "game/core/data_models.py",
        "game/core/world_generator.py",
        "game/core/npc_generator.py",
        "game/core/time_system.py",
        "game/core/behavior_system.py",
        "game/core/economy_system.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"OK {file_path}")
        else:
            print(f"MISSING {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"发现 {len(missing_files)} 个缺失文件")
        return False
    else:
        print("所有必需文件都存在")
        return True

def test_imports():
    """测试模块导入"""
    print("\n=== 模块导入测试 ===")
    
    # 添加game目录到Python路径
    game_path = os.path.join(os.getcwd(), 'game')
    if game_path not in sys.path:
        sys.path.insert(0, game_path)
    
    try:
        from core import DatabaseManager, WorldGenerator, World, Region, Block
        print("OK 核心模块导入成功")
        return True
    except Exception as e:
        print(f"ERROR 核心模块导入失败: {e}")
        return False

def test_initialization():
    """测试系统初始化"""
    print("\n=== 系统初始化测试 ===")
    
    try:
        # 添加game目录到Python路径
        game_path = os.path.join(os.getcwd(), 'game')
        if game_path not in sys.path:
            sys.path.insert(0, game_path)
        
        from core import DatabaseManager, WorldGenerator
        
        # 测试数据库管理器
        db_manager = DatabaseManager()
        print("OK 数据库管理器初始化成功")
        
        # 测试世界生成器
        world_generator = WorldGenerator(db_manager)
        print("OK 世界生成器初始化成功")
        
        # 测试变量设置
        current_world = None
        if current_world is None:
            print("OK current_world 变量正常")
        
        return True
        
    except Exception as e:
        print(f"ERROR 系统初始化失败: {e}")
        return False

def main():
    """主测试流程"""
    print("简化系统测试")
    print("=" * 40)
    
    tests = [
        ("文件结构", test_file_structure),
        ("模块导入", test_imports),
        ("系统初始化", test_initialization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行测试: {test_name}")
        if test_func():
            passed += 1
            print(f"结果: 通过")
        else:
            print(f"结果: 失败")
    
    print("\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("所有测试通过！系统应该可以正常运行")
        print("\n启动建议:")
        print("1. 双击运行 start_game.bat")
        print("2. 或使用Ren'Py启动器")
        print("3. 如果仍有问题，检查Ren'Py版本兼容性")
    else:
        print("部分测试失败，请检查错误信息")
        print("参考 QUICK_START.md 的故障排除部分")

if __name__ == "__main__":
    main()
