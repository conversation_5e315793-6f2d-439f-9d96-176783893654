#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试变量作用域问题
模拟Ren'Py的变量作用域行为
"""

import sys
import os

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

def simulate_renpy_init():
    """模拟Ren'Py的init python块"""
    print("=== 模拟 init python 块 ===")
    
    # 模拟全局变量声明
    global core_loaded, current_world
    core_loaded = False
    current_world = None
    
    print(f"初始状态: core_loaded = {core_loaded}")
    
    # 模拟导入过程
    try:
        print("尝试导入核心模块...")
        
        # 模拟sqlite3不可用
        import builtins
        original_import = builtins.__import__
        
        def mock_import(name, *args, **kwargs):
            if name == 'sqlite3':
                raise ImportError("No module named 'sqlite3'")
            return original_import(name, *args, **kwargs)
        
        builtins.__import__ = mock_import
        
        from core import DatabaseManager
        print("DatabaseManager 导入成功")
        
        from core import WorldGenerator
        print("WorldGenerator 导入成功")
        
        from core import World, Region, Block
        print("数据模型导入成功")
        
        # 设置变量
        core_loaded = True
        globals()['core_loaded'] = True
        print("所有核心模块导入成功")
        print(f"设置后 core_loaded = {core_loaded}")
        print(f"globals()中的 core_loaded = {globals().get('core_loaded')}")
        
        # 恢复导入
        builtins.__import__ = original_import
        
        return True
        
    except Exception as e:
        print(f"导入失败: {e}")
        core_loaded = False
        globals()['core_loaded'] = False
        return False

def simulate_renpy_start():
    """模拟Ren'Py的start标签"""
    print("\n=== 模拟 start 标签 ===")
    
    global core_loaded, current_world
    
    print(f"start标签中读取: core_loaded = {core_loaded}")
    print(f"start标签中读取: current_world = {current_world}")
    print(f"从globals()读取: core_loaded = {globals().get('core_loaded', 'NOT_FOUND')}")
    
    if core_loaded:
        print("✅ 核心系统加载成功！")
        return True
    else:
        print("❌ 核心系统加载失败")
        return False

def main():
    """主测试函数"""
    print("🧪 变量作用域测试")
    print("=" * 40)
    
    # 模拟init过程
    init_success = simulate_renpy_init()
    
    # 模拟start过程
    start_success = simulate_renpy_start()
    
    print("\n" + "=" * 40)
    print("测试结果:")
    print(f"  init过程: {'✅ 成功' if init_success else '❌ 失败'}")
    print(f"  start过程: {'✅ 成功' if start_success else '❌ 失败'}")
    
    if init_success and start_success:
        print("\n🎉 变量作用域测试通过！")
        print("Ren'Py游戏现在应该可以正确识别核心系统状态。")
    else:
        print("\n❌ 变量作用域测试失败")
        print("需要进一步调试变量传递问题。")

if __name__ == "__main__":
    main()
