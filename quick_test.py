#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证Ren'Py环境修复
"""

import sys
import os

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

def test_import():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        # 模拟Ren'Py环境（无sqlite3）
        if 'sqlite3' in sys.modules:
            del sys.modules['sqlite3']
        
        # 临时禁用sqlite3
        import builtins
        original_import = builtins.__import__
        
        def mock_import(name, *args, **kwargs):
            if name == 'sqlite3':
                raise ImportError("No module named 'sqlite3'")
            return original_import(name, *args, **kwargs)
        
        builtins.__import__ = mock_import
        
        # 测试导入
        from core import DatabaseManager, WorldGenerator, World
        print("✅ 核心模块导入成功")
        
        # 测试初始化
        db_manager = DatabaseManager()
        print("✅ 数据库管理器初始化成功")
        
        world_generator = WorldGenerator(db_manager)
        print("✅ 世界生成器初始化成功")
        
        # 恢复导入
        builtins.__import__ = original_import
        
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速修复验证")
    print("=" * 30)
    
    success = test_import()
    
    if success:
        print("\n🎉 修复验证成功！")
        print("Ren'Py环境现在应该可以正常加载核心系统。")
        print("\n下一步:")
        print("1. 启动Ren'Py游戏")
        print("2. 应该看到'✅ 核心系统加载成功！'")
        print("3. 而不是'❌ 核心系统加载失败'")
    else:
        print("\n❌ 修复验证失败")
        print("请检查错误信息")

if __name__ == "__main__":
    main()
