# -*- coding: utf-8 -*-
"""
TTS语音合成系统
支持多种TTS服务提供商和本地语音合成
"""

import os
import json
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import threading
import queue
import time

from .data_models import NPC, Gender, generate_uuid


class TTSProvider(Enum):
    """TTS服务提供商"""
    AZURE = "azure"
    GOOGLE = "google"
    OPENAI = "openai"
    ELEVENLABS = "elevenlabs"
    LOCAL = "local"
    MOCK = "mock"


class VoiceGender(Enum):
    """语音性别"""
    MALE = "male"
    FEMALE = "female"
    NEUTRAL = "neutral"


class VoiceStyle(Enum):
    """语音风格"""
    NEUTRAL = "neutral"
    CHEERFUL = "cheerful"
    SAD = "sad"
    ANGRY = "angry"
    EXCITED = "excited"
    CALM = "calm"
    WHISPER = "whisper"
    NEWS = "news"
    STORYTELLING = "storytelling"


@dataclass
class VoiceProfile:
    """语音配置文件"""
    voice_id: str
    name: str
    gender: VoiceGender
    language: str = "zh-CN"
    style: VoiceStyle = VoiceStyle.NEUTRAL
    speed: float = 1.0  # 语速 0.5-2.0
    pitch: float = 1.0  # 音调 0.5-2.0
    volume: float = 1.0  # 音量 0.0-1.0
    provider: TTSProvider = TTSProvider.MOCK
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "voice_id": self.voice_id,
            "name": self.name,
            "gender": self.gender.value,
            "language": self.language,
            "style": self.style.value,
            "speed": self.speed,
            "pitch": self.pitch,
            "volume": self.volume,
            "provider": self.provider.value
        }


@dataclass
class TTSRequest:
    """TTS请求"""
    text: str
    voice_profile: VoiceProfile
    output_format: str = "wav"  # wav, mp3, ogg
    sample_rate: int = 22050
    request_id: str = ""
    priority: int = 1  # 1-5, 5为最高优先级
    
    def __post_init__(self):
        if not self.request_id:
            self.request_id = generate_uuid()


@dataclass
class TTSResult:
    """TTS结果"""
    request_id: str
    success: bool
    audio_file_path: str = ""
    duration: float = 0.0  # 音频时长（秒）
    error_message: str = ""
    provider: str = ""
    generation_time: float = 0.0  # 生成耗时
    file_size: int = 0  # 文件大小（字节）


class TTSServiceInterface(ABC):
    """TTS服务接口"""
    
    @abstractmethod
    def synthesize(self, request: TTSRequest) -> TTSResult:
        """合成语音"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查服务是否可用"""
        pass
    
    @abstractmethod
    def get_available_voices(self) -> List[VoiceProfile]:
        """获取可用语音列表"""
        pass
    
    @abstractmethod
    def get_provider_name(self) -> str:
        """获取提供商名称"""
        pass


class MockTTSService(TTSServiceInterface):
    """模拟TTS服务（用于测试和离线使用）"""
    
    def __init__(self, audio_cache_dir: str = "audio_cache"):
        self.audio_cache_dir = audio_cache_dir
        os.makedirs(audio_cache_dir, exist_ok=True)
        
        # 预定义语音配置
        self.available_voices = [
            VoiceProfile("mock_male_1", "张三", VoiceGender.MALE, "zh-CN", VoiceStyle.NEUTRAL),
            VoiceProfile("mock_female_1", "李四", VoiceGender.FEMALE, "zh-CN", VoiceStyle.CHEERFUL),
            VoiceProfile("mock_male_2", "王五", VoiceGender.MALE, "zh-CN", VoiceStyle.CALM),
            VoiceProfile("mock_female_2", "赵六", VoiceGender.FEMALE, "zh-CN", VoiceStyle.NEWS),
            VoiceProfile("mock_neutral_1", "AI助手", VoiceGender.NEUTRAL, "zh-CN", VoiceStyle.STORYTELLING)
        ]
    
    def synthesize(self, request: TTSRequest) -> TTSResult:
        """模拟语音合成"""
        start_time = time.time()
        
        try:
            # 生成缓存文件名
            text_hash = hashlib.md5(request.text.encode('utf-8')).hexdigest()
            voice_hash = hashlib.md5(str(request.voice_profile.to_dict()).encode('utf-8')).hexdigest()
            cache_filename = f"mock_{voice_hash}_{text_hash}.{request.output_format}"
            cache_path = os.path.join(self.audio_cache_dir, cache_filename)
            
            # 模拟生成音频文件
            if not os.path.exists(cache_path):
                # 创建一个空的音频文件（实际应用中这里会调用真实的TTS API）
                with open(cache_path, 'wb') as f:
                    # 写入一些模拟的音频数据
                    mock_audio_data = b'\x00' * 1024  # 1KB的空数据
                    f.write(mock_audio_data)
                
                # 模拟处理时间
                time.sleep(0.1)
            
            # 计算音频时长（基于文字长度估算）
            estimated_duration = len(request.text) * 0.1  # 假设每个字符0.1秒
            
            generation_time = time.time() - start_time
            file_size = os.path.getsize(cache_path) if os.path.exists(cache_path) else 0
            
            return TTSResult(
                request_id=request.request_id,
                success=True,
                audio_file_path=cache_path,
                duration=estimated_duration,
                provider="mock",
                generation_time=generation_time,
                file_size=file_size
            )
            
        except Exception as e:
            return TTSResult(
                request_id=request.request_id,
                success=False,
                error_message=str(e),
                provider="mock",
                generation_time=time.time() - start_time
            )
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return True
    
    def get_available_voices(self) -> List[VoiceProfile]:
        """获取可用语音列表"""
        return self.available_voices.copy()
    
    def get_provider_name(self) -> str:
        """获取提供商名称"""
        return "Mock TTS Service"


class TTSManager:
    """TTS管理器"""
    
    def __init__(self, audio_cache_dir: str = "audio_cache"):
        self.audio_cache_dir = audio_cache_dir
        self.services: Dict[TTSProvider, TTSServiceInterface] = {}
        self.default_provider = TTSProvider.MOCK
        self.fallback_provider = TTSProvider.MOCK
        
        # 语音缓存
        self.audio_cache: Dict[str, TTSResult] = {}
        
        # 请求队列和工作线程
        self.request_queue = queue.PriorityQueue()
        self.result_callbacks: Dict[str, callable] = {}
        self.worker_thread = None
        self.is_running = False
        
        # 初始化默认服务
        self.services[TTSProvider.MOCK] = MockTTSService(audio_cache_dir)
        
        # NPC语音配置
        self.npc_voice_profiles: Dict[str, VoiceProfile] = {}
        
        os.makedirs(audio_cache_dir, exist_ok=True)
    
    def register_service(self, provider: TTSProvider, service: TTSServiceInterface):
        """注册TTS服务"""
        self.services[provider] = service
    
    def set_default_provider(self, provider: TTSProvider):
        """设置默认提供商"""
        if provider in self.services:
            self.default_provider = provider
    
    def start_worker(self):
        """启动工作线程"""
        if not self.is_running:
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
    
    def stop_worker(self):
        """停止工作线程"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join()
    
    def _worker_loop(self):
        """工作线程循环"""
        while self.is_running:
            try:
                # 获取请求（带超时）
                priority, request = self.request_queue.get(timeout=1.0)
                
                # 处理请求
                result = self._process_request(request)
                
                # 缓存结果
                if result.success:
                    cache_key = self._generate_cache_key(request)
                    self.audio_cache[cache_key] = result
                
                # 调用回调函数
                if request.request_id in self.result_callbacks:
                    callback = self.result_callbacks.pop(request.request_id)
                    callback(result)
                
                self.request_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"TTS工作线程错误: {e}")
    
    def synthesize_async(self, request: TTSRequest, callback: callable = None) -> str:
        """异步合成语音"""
        # 检查缓存
        cache_key = self._generate_cache_key(request)
        if cache_key in self.audio_cache:
            cached_result = self.audio_cache[cache_key]
            if callback:
                callback(cached_result)
            return request.request_id
        
        # 添加到队列
        if callback:
            self.result_callbacks[request.request_id] = callback
        
        # 优先级队列：数字越小优先级越高
        priority = 6 - request.priority
        self.request_queue.put((priority, request))
        
        # 确保工作线程运行
        if not self.is_running:
            self.start_worker()
        
        return request.request_id
    
    def synthesize_sync(self, request: TTSRequest) -> TTSResult:
        """同步合成语音"""
        # 检查缓存
        cache_key = self._generate_cache_key(request)
        if cache_key in self.audio_cache:
            return self.audio_cache[cache_key]
        
        # 直接处理请求
        result = self._process_request(request)
        
        # 缓存结果
        if result.success:
            self.audio_cache[cache_key] = result
        
        return result
    
    def _process_request(self, request: TTSRequest) -> TTSResult:
        """处理TTS请求"""
        provider = request.voice_profile.provider
        
        # 尝试使用指定的提供商
        if provider in self.services:
            service = self.services[provider]
            if service.is_available():
                return service.synthesize(request)
        
        # 故障转移到默认提供商
        if self.default_provider in self.services and self.default_provider != provider:
            default_service = self.services[self.default_provider]
            if default_service.is_available():
                # 更新请求的提供商
                request.voice_profile.provider = self.default_provider
                result = default_service.synthesize(request)
                result.provider += " (fallback)"
                return result
        
        # 所有服务都不可用
        return TTSResult(
            request_id=request.request_id,
            success=False,
            error_message="All TTS services unavailable",
            provider="none"
        )
    
    def _generate_cache_key(self, request: TTSRequest) -> str:
        """生成缓存键"""
        key_data = {
            "text": request.text,
            "voice_profile": request.voice_profile.to_dict(),
            "output_format": request.output_format,
            "sample_rate": request.sample_rate
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()
    
    def get_npc_voice_profile(self, npc: NPC) -> VoiceProfile:
        """获取NPC的语音配置"""
        if npc.id in self.npc_voice_profiles:
            return self.npc_voice_profiles[npc.id]
        
        # 根据NPC属性自动分配语音
        voice_profile = self._auto_assign_voice(npc)
        self.npc_voice_profiles[npc.id] = voice_profile
        return voice_profile
    
    def _auto_assign_voice(self, npc: NPC) -> VoiceProfile:
        """自动为NPC分配语音"""
        available_voices = self.services[self.default_provider].get_available_voices()
        
        # 根据性别筛选
        gender_voices = [
            v for v in available_voices 
            if v.gender.value == npc.gender.value or v.gender == VoiceGender.NEUTRAL
        ]
        
        if not gender_voices:
            gender_voices = available_voices
        
        # 根据年龄和性格选择语音风格
        if npc.age < 25:
            preferred_styles = [VoiceStyle.CHEERFUL, VoiceStyle.EXCITED]
        elif npc.age > 60:
            preferred_styles = [VoiceStyle.CALM, VoiceStyle.NEUTRAL]
        else:
            preferred_styles = [VoiceStyle.NEUTRAL, VoiceStyle.CALM]
        
        # 根据性格调整
        if npc.personality.extraversion > 70:
            preferred_styles.insert(0, VoiceStyle.CHEERFUL)
        elif npc.personality.neuroticism > 70:
            preferred_styles.append(VoiceStyle.WHISPER)
        
        # 选择最匹配的语音
        for style in preferred_styles:
            matching_voices = [v for v in gender_voices if v.style == style]
            if matching_voices:
                selected_voice = matching_voices[0]
                break
        else:
            selected_voice = gender_voices[0] if gender_voices else available_voices[0]
        
        # 根据NPC属性调整语音参数
        voice_profile = VoiceProfile(
            voice_id=selected_voice.voice_id,
            name=selected_voice.name,
            gender=selected_voice.gender,
            language=selected_voice.language,
            style=selected_voice.style,
            speed=1.0 + (npc.personality.extraversion - 50) * 0.01,  # 外向的人说话更快
            pitch=1.0 + (npc.age - 40) * -0.005,  # 年龄影响音调
            volume=0.8 + (npc.personality.extraversion) * 0.004,  # 外向的人声音更大
            provider=self.default_provider
        )
        
        # 确保参数在合理范围内
        voice_profile.speed = max(0.5, min(2.0, voice_profile.speed))
        voice_profile.pitch = max(0.5, min(2.0, voice_profile.pitch))
        voice_profile.volume = max(0.1, min(1.0, voice_profile.volume))
        
        return voice_profile
    
    def set_npc_voice_profile(self, npc_id: str, voice_profile: VoiceProfile):
        """设置NPC的语音配置"""
        self.npc_voice_profiles[npc_id] = voice_profile
    
    def get_available_voices(self, provider: Optional[TTSProvider] = None) -> List[VoiceProfile]:
        """获取可用语音列表"""
        target_provider = provider or self.default_provider
        
        if target_provider in self.services:
            service = self.services[target_provider]
            if service.is_available():
                return service.get_available_voices()
        
        return []
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        status = {
            "default_provider": self.default_provider.value,
            "fallback_provider": self.fallback_provider.value,
            "worker_running": self.is_running,
            "queue_size": self.request_queue.qsize(),
            "cache_size": len(self.audio_cache),
            "npc_voices": len(self.npc_voice_profiles),
            "services": {}
        }
        
        for provider, service in self.services.items():
            status["services"][provider.value] = {
                "available": service.is_available(),
                "name": service.get_provider_name(),
                "voices": len(service.get_available_voices())
            }
        
        return status
    
    def clear_cache(self):
        """清理缓存"""
        self.audio_cache.clear()
        
        # 删除缓存文件
        if os.path.exists(self.audio_cache_dir):
            for filename in os.listdir(self.audio_cache_dir):
                file_path = os.path.join(self.audio_cache_dir, filename)
                try:
                    os.remove(file_path)
                except Exception as e:
                    print(f"删除缓存文件失败 {file_path}: {e}")


# 全局TTS管理器实例
tts_manager = TTSManager()
