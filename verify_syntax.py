#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Ren'Py语法
"""

import os
import subprocess
import sys

def check_renpy_syntax():
    """检查Ren'Py语法"""
    print("检查Ren'Py语法...")
    
    # 查找所有.rpy文件
    rpy_files = []
    for root, dirs, files in os.walk('game'):
        for file in files:
            if file.endswith('.rpy'):
                rpy_files.append(os.path.join(root, file))
    
    print(f"找到 {len(rpy_files)} 个.rpy文件:")
    for file in rpy_files:
        print(f"  - {file}")
    
    # 检查标签重复
    print("\n检查标签定义...")
    labels = {}
    
    for file_path in rpy_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for i, line in enumerate(lines, 1):
                    line = line.strip()
                    if line.startswith('label ') and ':' in line:
                        label_name = line.split()[1].rstrip(':')
                        if label_name in labels:
                            print(f"❌ 重复标签 '{label_name}':")
                            print(f"   第一次定义: {labels[label_name]}")
                            print(f"   重复定义: {file_path}:{i}")
                            return False
                        else:
                            labels[label_name] = f"{file_path}:{i}"
                            print(f"✓ 标签 '{label_name}' 在 {file_path}:{i}")
        except Exception as e:
            print(f"❌ 读取文件 {file_path} 失败: {e}")
            return False
    
    print(f"\n✅ 检查完成，找到 {len(labels)} 个唯一标签，无重复")
    return True

def main():
    print("🔍 Ren'Py语法验证工具")
    print("=" * 40)
    
    if not os.path.exists('game'):
        print("❌ 找不到game目录")
        return
    
    if check_renpy_syntax():
        print("\n🎉 语法检查通过！")
        print("现在可以尝试启动Ren'Py了")
    else:
        print("\n❌ 发现语法问题，请修复后重试")

if __name__ == "__main__":
    main()
