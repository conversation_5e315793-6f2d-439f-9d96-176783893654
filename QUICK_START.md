# 快速启动指南

## 🎮 如何启动游戏

### 方法1: 使用启动脚本（最简单）
双击运行 `start_game.bat` 文件，它会自动查找并启动Ren'Py。

### 方法2: 使用Ren'Py启动器（推荐）
1. 下载并安装 [Ren'Py SDK](https://www.renpy.org/latest.html)
2. 启动Ren'Py启动器
3. 点击"选择项目"，选择本项目目录 (`c:\gamelab\hf`)
4. 点击"启动项目"

### 方法3: 命令行启动
```bash
# 在项目根目录下运行
renpy.exe .
```

## 🎯 游戏功能体验

### 1. 世界浏览系统
- 游戏启动后会自动进入世界浏览器
- 如果没有现有世界，会自动生成一个新世界
- 可以浏览多级行政区划：国家 → 省份 → 城市 → 区/村
- 查看各级区域的人口、经济等统计信息

### 2. NPC管理系统
- 在世界浏览器中点击"NPC管理"进入
- 点击"生成NPC"创建测试NPC（每次生成5个）
- 查看NPC的详细信息：
  - 基础属性（年龄、性别、教育等）
  - 经济状况（现金、存款、收入等）
  - 工作信息（职位、薪资等）
  - 日常状态（精力、幸福度、压力、健康）

### 3. 时间推进系统
- 在NPC管理界面点击"时间推进"
- 系统会推进1小时游戏时间
- 观察NPC状态的变化和活动执行
- 查看活动完成情况和状态变化

### 4. 经济报告
- 点击"经济报告"查看系统经济状况
- 包含总体统计、就业统计、市场信息等

## 🧪 测试功能

### 运行核心系统测试
```bash
# 测试M0阶段（世界生成）
python test_core.py

# 测试M1阶段（NPC系统）
python test_m1_system.py

# 检查项目语法和结构
python test_renpy_syntax.py
```

## 📊 当前功能状态

### ✅ 已完成功能
- **M0**: 世界地图生成与浏览系统
- **M1**: 单NPC回合制日常系统
  - 完整的NPC属性系统
  - 24小时时间推进
  - 智能行为系统
  - 基础经济系统
  - NPC管理界面

### 🔄 开发中功能
- **M2**: 多NPC经济结算系统
- **M3**: AI事件流与新闻系统
- **M4**: 角色切换与上帝模式
- **M5**: AI演出系统（TTS、文生图等）

## 🎮 游戏操作指南

### 世界浏览器
- **导航**: 点击区域名称进入下级区域
- **返回**: 点击"返回上级"返回上级区域
- **信息**: 查看区域的人口、GDP、基础设施等信息
- **区块详情**: 点击"详情"查看区块的详细信息

### NPC管理器
- **生成NPC**: 创建随机属性的测试NPC
- **查看详情**: 点击NPC列表中的"详情"按钮
- **状态监控**: 观察NPC的精力条、幸福度条等实时状态
- **时间控制**: 手动推进游戏时间，观察NPC行为变化

### 界面说明
- **状态条颜色**:
  - 🟡 精力条（橙色）
  - 🟢 幸福度条（绿色）  
  - 🔴 压力条（红色）
  - 🔵 健康条（蓝色）
- **NPC状态指示器**:
  - 🟢 活跃NPC
  - ⚪ 非活跃NPC

## 🐛 常见问题

### Q: 游戏启动时出现"Parsing the script failed"错误
A: 这通常是Ren'Py脚本语法错误，解决方法：
1. 运行 `python test_renpy_syntax.py` 检查语法
2. 查看具体错误信息中的文件名和行号
3. 参考 `RENPY_SYNTAX_GUIDE.md` 修复常见语法问题
4. 常见问题：
   - `textbutton` 使用 `text_size` 而不是 `size`
   - 属性中的条件表达式需要先在python块中计算

### Q: "not a valid child of the frame statement"错误
A: 这是结构错误，通常是：
1. `if` 语句不能直接作为某些容器的子元素
2. 条件表达式不能直接用在属性中
3. 解决方法：用 `vbox` 或 `hbox` 包装，或先用python计算

### Q: "An exception has occurred" 或 "current_world is not defined"错误
A: 这是核心系统初始化失败，解决方法：
1. 运行 `python diagnose_system.py` 进行系统诊断
2. 运行 `python test_renpy_import.py` 测试导入环境
3. 清理Python缓存：删除 `game/core/__pycache__` 目录
4. 检查Python版本兼容性（推荐Python 3.8-3.11）
5. 确保所有核心模块文件完整存在

### Q: 核心系统导入失败
A: 确保Python路径正确，运行测试脚本检查模块完整性：
1. 运行 `python diagnose_system.py` 检查系统状态
2. 检查文件结构是否完整
3. 确保Python环境正确配置

### Q: NPC生成失败
A: 检查数据库是否正常初始化，查看错误日志信息。

### Q: 时间推进没有反应
A: 确保有活跃的NPC，检查系统初始化状态。

### Q: 界面显示异常
A: 检查变量是否正确定义，确保所有python块中的变量都有默认值。

## 📝 开发说明

### 项目结构
```
game/
├── core/                    # 核心系统模块
│   ├── data_models.py      # 数据模型
│   ├── database.py         # 数据库管理
│   ├── world_generator.py  # 世界生成
│   ├── npc_generator.py    # NPC生成
│   ├── time_system.py      # 时间系统
│   ├── behavior_system.py  # 行为系统
│   └── economy_system.py   # 经济系统
├── script.rpy             # 主游戏脚本
├── world_browser.rpy      # 世界浏览界面
├── npc_manager.rpy        # NPC管理界面
└── saves/                 # 存档目录
```

### 数据存储
- 使用SQLite数据库存储所有游戏数据
- 支持复杂对象的JSON序列化
- 自动创建数据库表和索引
- 支持数据的增量更新和查询

### 扩展开发
- 所有核心功能都是模块化设计
- 可以轻松添加新的NPC行为类型
- 支持自定义活动和意图类型
- 经济系统支持新商品和交易类型

---

**版本**: M1 完成  
**最后更新**: 2025-07-14  
**开发状态**: 活跃开发中
