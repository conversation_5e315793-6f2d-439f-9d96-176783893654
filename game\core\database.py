# -*- coding: utf-8 -*-
"""
数据库管理器
处理游戏数据的持久化存储和查询操作
"""

try:
    import sqlite3
    SQLITE_AVAILABLE = True
except ImportError:
    SQLITE_AVAILABLE = False
    # 在Ren'Py环境中，我们将使用文件存储作为替代
    import pickle

import json
import os
from typing import List, Optional, Dict, Any
from datetime import datetime
from .data_models import (
    World, Region, Block, Building, Organization, Policy, Relationship,
    RegionType, BuildingType, OrganizationType,
    NPC, Job, Skill, PersonalityTrait, DailyStats, Goal, Activity, NPCRelationship,
    Gender, EducationLevel, JobType, ActivityType
)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "game/saves/world.db"):
        self.db_path = db_path
        self.ensure_directory()
        self.init_database()

    def ensure_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir:  # 只有当目录路径不为空时才创建
            os.makedirs(db_dir, exist_ok=True)
    
    def get_connection(self):
        """获取数据库连接"""
        if not SQLITE_AVAILABLE:
            raise ImportError("SQLite3 not available")
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以按列名访问
        return conn
    
    def init_database(self):
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            # 世界表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS worlds (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    version TEXT DEFAULT '1.0',
                    total_population INTEGER DEFAULT 0,
                    total_regions INTEGER DEFAULT 0,
                    total_blocks INTEGER DEFAULT 0,
                    global_inflation_rate REAL DEFAULT 0.02,
                    global_growth_rate REAL DEFAULT 0.03,
                    base_currency TEXT DEFAULT 'credits',
                    current_day INTEGER DEFAULT 1,
                    current_season TEXT DEFAULT 'spring',
                    current_year INTEGER DEFAULT 2024,
                    world_events TEXT DEFAULT '[]',
                    global_policies TEXT DEFAULT '[]',
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')
            
            # 区域表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS regions (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    parent_id TEXT,
                    children_ids TEXT DEFAULT '[]',
                    policies TEXT DEFAULT '[]',
                    gov_balance REAL DEFAULT 0.0,
                    tax_rate REAL DEFAULT 0.1,
                    population INTEGER DEFAULT 0,
                    gdp REAL DEFAULT 0.0,
                    relationships TEXT DEFAULT '[]',
                    infrastructure_score REAL DEFAULT 50.0,
                    education_level REAL DEFAULT 50.0,
                    healthcare_level REAL DEFAULT 50.0,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (parent_id) REFERENCES regions (id)
                )
            ''')
            
            # 区块表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS blocks (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    parent_id TEXT NOT NULL,
                    land_area REAL NOT NULL,
                    buildable_index_max REAL NOT NULL,
                    buildable_index_used REAL DEFAULT 0.0,
                    population INTEGER DEFAULT 0,
                    population_cap INTEGER DEFAULT 0,
                    infrastructure_score REAL DEFAULT 50.0,
                    buildings TEXT DEFAULT '[]',
                    land_price REAL DEFAULT 1000.0,
                    development_level REAL DEFAULT 1.0,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (parent_id) REFERENCES regions (id)
                )
            ''')
            
            # 建筑表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS buildings (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    block_id TEXT NOT NULL,
                    floor_area REAL NOT NULL,
                    capacity_index REAL NOT NULL,
                    max_occupants INTEGER NOT NULL,
                    organizations TEXT DEFAULT '[]',
                    residents TEXT DEFAULT '[]',
                    construction_cost REAL DEFAULT 0.0,
                    maintenance_cost REAL DEFAULT 0.0,
                    rent_price REAL DEFAULT 0.0,
                    condition REAL DEFAULT 100.0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (block_id) REFERENCES blocks (id)
                )
            ''')
            
            # 组织表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS organizations (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    capital REAL DEFAULT 0.0,
                    reputation REAL DEFAULT 50.0,
                    employees TEXT DEFAULT '[]',
                    max_employees INTEGER DEFAULT 10,
                    branches TEXT DEFAULT '[]',
                    headquarters TEXT,
                    policies TEXT DEFAULT '[]',
                    industry TEXT DEFAULT 'general',
                    business_model TEXT DEFAULT '',
                    monthly_revenue REAL DEFAULT 0.0,
                    monthly_expenses REAL DEFAULT 0.0,
                    is_active BOOLEAN DEFAULT 1,
                    founded_date TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')
            
            # NPC表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS npcs (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    age INTEGER NOT NULL,
                    gender TEXT NOT NULL,
                    education_level TEXT NOT NULL,
                    birth_date TEXT,
                    birth_place TEXT,
                    current_residence TEXT,
                    family_members TEXT DEFAULT '[]',
                    marital_status TEXT DEFAULT 'single',
                    cash REAL DEFAULT 1000.0,
                    bank_balance REAL DEFAULT 5000.0,
                    assets TEXT DEFAULT '{}',
                    debts TEXT DEFAULT '{}',
                    monthly_income REAL DEFAULT 0.0,
                    monthly_expenses REAL DEFAULT 0.0,
                    current_job TEXT,
                    job_history TEXT DEFAULT '[]',
                    personality TEXT,
                    npc_values TEXT DEFAULT '{}',
                    goals TEXT DEFAULT '[]',
                    skills TEXT DEFAULT '{}',
                    daily_stats TEXT,
                    relationships TEXT DEFAULT '[]',
                    activity_history TEXT DEFAULT '[]',
                    current_activity TEXT,
                    consumption_preferences TEXT DEFAULT '{}',
                    current_location TEXT,
                    home_location TEXT,
                    work_location TEXT,
                    daily_schedule TEXT DEFAULT '{}',
                    is_active BOOLEAN DEFAULT 1,
                    is_player_controlled BOOLEAN DEFAULT 0,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # 工作表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS jobs (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    job_type TEXT NOT NULL,
                    organization_id TEXT,
                    building_id TEXT,
                    salary REAL DEFAULT 0.0,
                    work_hours_per_day INTEGER DEFAULT 8,
                    required_skills TEXT DEFAULT '[]',
                    work_schedule TEXT DEFAULT '{}',
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (organization_id) REFERENCES organizations (id),
                    FOREIGN KEY (building_id) REFERENCES buildings (id)
                )
            ''')

            # 活动表
            conn.execute('''
                CREATE TABLE IF NOT EXISTS activities (
                    id TEXT PRIMARY KEY,
                    npc_id TEXT NOT NULL,
                    type TEXT NOT NULL,
                    name TEXT NOT NULL,
                    duration INTEGER NOT NULL,
                    energy_cost REAL DEFAULT 0.0,
                    happiness_gain REAL DEFAULT 0.0,
                    stress_change REAL DEFAULT 0.0,
                    location TEXT,
                    requirements TEXT DEFAULT '{}',
                    rewards TEXT DEFAULT '{}',
                    start_time TEXT,
                    end_time TEXT,
                    created_at TEXT,
                    FOREIGN KEY (npc_id) REFERENCES npcs (id)
                )
            ''')

            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_regions_parent ON regions(parent_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_regions_type ON regions(type)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_blocks_parent ON blocks(parent_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_buildings_block ON buildings(block_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_organizations_type ON organizations(type)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_npcs_location ON npcs(current_location)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_npcs_residence ON npcs(current_residence)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_jobs_organization ON jobs(organization_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_activities_npc ON activities(npc_id)')
    
    def save_world(self, world: World) -> bool:
        """保存世界数据"""
        try:
            with self.get_connection() as conn:
                now = datetime.now().isoformat()
                world.updated_at = now
                if not world.created_at:
                    world.created_at = now
                
                conn.execute('''
                    INSERT OR REPLACE INTO worlds 
                    (id, name, version, total_population, total_regions, total_blocks,
                     global_inflation_rate, global_growth_rate, base_currency,
                     current_day, current_season, current_year,
                     world_events, global_policies, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    world.id, world.name, world.version, world.total_population,
                    world.total_regions, world.total_blocks, world.global_inflation_rate,
                    world.global_growth_rate, world.base_currency, world.current_day,
                    world.current_season, world.current_year,
                    json.dumps(world.world_events, ensure_ascii=False),
                    json.dumps([p.__dict__ for p in world.global_policies], ensure_ascii=False),
                    world.created_at, world.updated_at
                ))
            return True
        except Exception as e:
            print(f"保存世界数据失败: {e}")
            return False
    
    def load_world(self, world_id: str) -> Optional[World]:
        """加载世界数据"""
        try:
            with self.get_connection() as conn:
                row = conn.execute('SELECT * FROM worlds WHERE id = ?', (world_id,)).fetchone()
                if not row:
                    return None
                
                world_events = json.loads(row['world_events']) if row['world_events'] else []
                global_policies_data = json.loads(row['global_policies']) if row['global_policies'] else []
                global_policies = [Policy(**p) for p in global_policies_data]
                
                return World(
                    id=row['id'],
                    name=row['name'],
                    version=row['version'],
                    total_population=row['total_population'],
                    total_regions=row['total_regions'],
                    total_blocks=row['total_blocks'],
                    global_inflation_rate=row['global_inflation_rate'],
                    global_growth_rate=row['global_growth_rate'],
                    base_currency=row['base_currency'],
                    current_day=row['current_day'],
                    current_season=row['current_season'],
                    current_year=row['current_year'],
                    world_events=world_events,
                    global_policies=global_policies,
                    created_at=row['created_at'],
                    updated_at=row['updated_at']
                )
        except Exception as e:
            print(f"加载世界数据失败: {e}")
            return None
    
    def save_region(self, region: Region) -> bool:
        """保存区域数据"""
        try:
            with self.get_connection() as conn:
                now = datetime.now().isoformat()
                region.updated_at = now
                if not region.created_at:
                    region.created_at = now
                
                conn.execute('''
                    INSERT OR REPLACE INTO regions 
                    (id, name, type, parent_id, children_ids, policies, gov_balance,
                     tax_rate, population, gdp, relationships, infrastructure_score,
                     education_level, healthcare_level, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    region.id, region.name, region.type.value, region.parent_id,
                    json.dumps(region.children_ids),
                    json.dumps([p.__dict__ for p in region.policies], ensure_ascii=False),
                    region.gov_balance, region.tax_rate, region.population, region.gdp,
                    json.dumps([r.__dict__ for r in region.relationships], ensure_ascii=False),
                    region.infrastructure_score, region.education_level, region.healthcare_level,
                    region.created_at, region.updated_at
                ))
            return True
        except Exception as e:
            print(f"保存区域数据失败: {e}")
            return False
    
    def load_regions_by_parent(self, parent_id: Optional[str] = None) -> List[Region]:
        """根据父级ID加载区域列表"""
        try:
            with self.get_connection() as conn:
                if parent_id is None:
                    rows = conn.execute('SELECT * FROM regions WHERE parent_id IS NULL').fetchall()
                else:
                    rows = conn.execute('SELECT * FROM regions WHERE parent_id = ?', (parent_id,)).fetchall()
                
                regions = []
                for row in rows:
                    policies_data = json.loads(row['policies']) if row['policies'] else []
                    policies = [Policy(**p) for p in policies_data]
                    
                    relationships_data = json.loads(row['relationships']) if row['relationships'] else []
                    relationships = [Relationship(**r) for r in relationships_data]
                    
                    region = Region(
                        id=row['id'],
                        name=row['name'],
                        type=RegionType(row['type']),
                        parent_id=row['parent_id'],
                        children_ids=json.loads(row['children_ids']) if row['children_ids'] else [],
                        policies=policies,
                        gov_balance=row['gov_balance'],
                        tax_rate=row['tax_rate'],
                        population=row['population'],
                        gdp=row['gdp'],
                        relationships=relationships,
                        infrastructure_score=row['infrastructure_score'],
                        education_level=row['education_level'],
                        healthcare_level=row['healthcare_level'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
                    regions.append(region)
                
                return regions
        except Exception as e:
            print(f"加载区域数据失败: {e}")
            return []
    
    def get_region_by_id(self, region_id: str) -> Optional[Region]:
        """根据ID获取单个区域"""
        regions = self.load_regions_by_parent(None)  # 先加载所有顶级区域
        # 这里需要递归搜索，暂时简化实现
        with self.get_connection() as conn:
            row = conn.execute('SELECT * FROM regions WHERE id = ?', (region_id,)).fetchone()
            if not row:
                return None
            
            policies_data = json.loads(row['policies']) if row['policies'] else []
            policies = [Policy(**p) for p in policies_data]
            
            relationships_data = json.loads(row['relationships']) if row['relationships'] else []
            relationships = [Relationship(**r) for r in relationships_data]
            
            return Region(
                id=row['id'],
                name=row['name'],
                type=RegionType(row['type']),
                parent_id=row['parent_id'],
                children_ids=json.loads(row['children_ids']) if row['children_ids'] else [],
                policies=policies,
                gov_balance=row['gov_balance'],
                tax_rate=row['tax_rate'],
                population=row['population'],
                gdp=row['gdp'],
                relationships=relationships,
                infrastructure_score=row['infrastructure_score'],
                education_level=row['education_level'],
                healthcare_level=row['healthcare_level'],
                created_at=row['created_at'],
                updated_at=row['updated_at']
            )
    
    def list_worlds(self) -> List[Dict[str, Any]]:
        """列出所有世界"""
        try:
            with self.get_connection() as conn:
                rows = conn.execute('SELECT id, name, version, created_at, updated_at FROM worlds').fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            print(f"列出世界失败: {e}")
            return []
    
    def save_block(self, block: Block) -> bool:
        """保存区块数据"""
        try:
            with self.get_connection() as conn:
                now = datetime.now().isoformat()
                block.updated_at = now
                if not block.created_at:
                    block.created_at = now

                conn.execute('''
                    INSERT OR REPLACE INTO blocks
                    (id, name, type, parent_id, land_area, buildable_index_max,
                     buildable_index_used, population, population_cap, infrastructure_score,
                     buildings, land_price, development_level, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    block.id, block.name, block.type.value, block.parent_id,
                    block.land_area, block.buildable_index_max, block.buildable_index_used,
                    block.population, block.population_cap, block.infrastructure_score,
                    json.dumps(block.buildings), block.land_price, block.development_level,
                    block.created_at, block.updated_at
                ))
            return True
        except Exception as e:
            print(f"保存区块数据失败: {e}")
            return False

    def load_blocks_by_parent(self, parent_id: str) -> List[Block]:
        """根据父级ID加载区块列表"""
        try:
            with self.get_connection() as conn:
                rows = conn.execute('SELECT * FROM blocks WHERE parent_id = ?', (parent_id,)).fetchall()

                blocks = []
                for row in rows:
                    block = Block(
                        id=row['id'],
                        name=row['name'],
                        type=RegionType(row['type']),
                        parent_id=row['parent_id'],
                        land_area=row['land_area'],
                        buildable_index_max=row['buildable_index_max'],
                        buildable_index_used=row['buildable_index_used'],
                        population=row['population'],
                        population_cap=row['population_cap'],
                        infrastructure_score=row['infrastructure_score'],
                        buildings=json.loads(row['buildings']) if row['buildings'] else [],
                        land_price=row['land_price'],
                        development_level=row['development_level'],
                        created_at=row['created_at'],
                        updated_at=row['updated_at']
                    )
                    blocks.append(block)

                return blocks
        except Exception as e:
            print(f"加载区块数据失败: {e}")
            return []

    def save_npc(self, npc: NPC) -> bool:
        """保存NPC数据"""
        try:
            with self.get_connection() as conn:
                now = datetime.now().isoformat()
                npc.updated_at = now
                if not npc.created_at:
                    npc.created_at = now

                # 序列化复杂对象
                current_job_json = json.dumps(npc.current_job.__dict__ if npc.current_job else None, ensure_ascii=False)
                job_history_json = json.dumps([job.__dict__ for job in npc.job_history], ensure_ascii=False)
                personality_json = json.dumps(npc.personality.__dict__, ensure_ascii=False)
                daily_stats_json = json.dumps(npc.daily_stats.__dict__, ensure_ascii=False)
                skills_json = json.dumps({k: v.__dict__ for k, v in npc.skills.items()}, ensure_ascii=False)
                goals_json = json.dumps([goal.__dict__ for goal in npc.goals], ensure_ascii=False)
                relationships_json = json.dumps([rel.__dict__ for rel in npc.relationships], ensure_ascii=False)
                # 序列化活动历史，处理枚举类型
                activity_history_data = []
                for act in npc.activity_history:
                    act_dict = act.__dict__.copy()
                    act_dict['type'] = act.type.value if hasattr(act.type, 'value') else str(act.type)
                    activity_history_data.append(act_dict)
                activity_history_json = json.dumps(activity_history_data, ensure_ascii=False)

                # 序列化当前活动
                current_activity_data = None
                if npc.current_activity:
                    current_activity_data = npc.current_activity.__dict__.copy()
                    current_activity_data['type'] = npc.current_activity.type.value if hasattr(npc.current_activity.type, 'value') else str(npc.current_activity.type)
                current_activity_json = json.dumps(current_activity_data, ensure_ascii=False)

                conn.execute('''
                    INSERT OR REPLACE INTO npcs
                    (id, name, age, gender, education_level, birth_date, birth_place,
                     current_residence, family_members, marital_status, cash, bank_balance,
                     assets, debts, monthly_income, monthly_expenses, current_job, job_history,
                     personality, npc_values, goals, skills, daily_stats, relationships,
                     activity_history, current_activity, consumption_preferences,
                     current_location, home_location, work_location, daily_schedule,
                     is_active, is_player_controlled, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    npc.id, npc.name, npc.age, npc.gender.value, npc.education_level.value,
                    npc.birth_date, npc.birth_place, npc.current_residence,
                    json.dumps(npc.family_members), npc.marital_status, npc.cash, npc.bank_balance,
                    json.dumps(npc.assets, ensure_ascii=False), json.dumps(npc.debts, ensure_ascii=False),
                    npc.monthly_income, npc.monthly_expenses, current_job_json, job_history_json,
                    personality_json, json.dumps(npc.values, ensure_ascii=False), goals_json,
                    skills_json, daily_stats_json, relationships_json, activity_history_json,
                    current_activity_json, json.dumps(npc.consumption_preferences, ensure_ascii=False),
                    npc.current_location, npc.home_location, npc.work_location,
                    json.dumps(npc.daily_schedule, ensure_ascii=False), npc.is_active,
                    npc.is_player_controlled, npc.created_at, npc.updated_at
                ))
            return True
        except Exception as e:
            print(f"保存NPC数据失败: {e}")
            return False

    def load_npc(self, npc_id: str) -> Optional[NPC]:
        """加载单个NPC数据"""
        try:
            with self.get_connection() as conn:
                row = conn.execute('SELECT * FROM npcs WHERE id = ?', (npc_id,)).fetchone()
                if not row:
                    return None

                # 反序列化复杂对象
                current_job_data = json.loads(row['current_job']) if row['current_job'] else None
                current_job = Job(**current_job_data) if current_job_data else None

                job_history_data = json.loads(row['job_history']) if row['job_history'] else []
                job_history = [Job(**job_data) for job_data in job_history_data]

                personality_data = json.loads(row['personality']) if row['personality'] else {}
                personality = PersonalityTrait(**personality_data)

                daily_stats_data = json.loads(row['daily_stats']) if row['daily_stats'] else {}
                daily_stats = DailyStats(**daily_stats_data)

                skills_data = json.loads(row['skills']) if row['skills'] else {}
                skills = {k: Skill(**v) for k, v in skills_data.items()}

                goals_data = json.loads(row['goals']) if row['goals'] else []
                goals = [Goal(**goal_data) for goal_data in goals_data]

                relationships_data = json.loads(row['relationships']) if row['relationships'] else []
                relationships = [NPCRelationship(**rel_data) for rel_data in relationships_data]

                activity_history_data = json.loads(row['activity_history']) if row['activity_history'] else []
                activity_history = []
                for act_data in activity_history_data:
                    if 'type' in act_data and isinstance(act_data['type'], str):
                        act_data['type'] = ActivityType(act_data['type'])
                    activity_history.append(Activity(**act_data))

                current_activity_data = json.loads(row['current_activity']) if row['current_activity'] else None
                current_activity = None
                if current_activity_data:
                    if 'type' in current_activity_data and isinstance(current_activity_data['type'], str):
                        current_activity_data['type'] = ActivityType(current_activity_data['type'])
                    current_activity = Activity(**current_activity_data)

                return NPC(
                    id=row['id'],
                    name=row['name'],
                    age=row['age'],
                    gender=Gender(row['gender']),
                    education_level=EducationLevel(row['education_level']),
                    birth_date=row['birth_date'],
                    birth_place=row['birth_place'],
                    current_residence=row['current_residence'],
                    family_members=json.loads(row['family_members']) if row['family_members'] else [],
                    marital_status=row['marital_status'],
                    cash=row['cash'],
                    bank_balance=row['bank_balance'],
                    assets=json.loads(row['assets']) if row['assets'] else {},
                    debts=json.loads(row['debts']) if row['debts'] else {},
                    monthly_income=row['monthly_income'],
                    monthly_expenses=row['monthly_expenses'],
                    current_job=current_job,
                    job_history=job_history,
                    personality=personality,
                    values=json.loads(row['npc_values']) if row['npc_values'] else {},
                    goals=goals,
                    skills=skills,
                    daily_stats=daily_stats,
                    relationships=relationships,
                    activity_history=activity_history,
                    current_activity=current_activity,
                    consumption_preferences=json.loads(row['consumption_preferences']) if row['consumption_preferences'] else {},
                    current_location=row['current_location'],
                    home_location=row['home_location'],
                    work_location=row['work_location'],
                    daily_schedule=json.loads(row['daily_schedule']) if row['daily_schedule'] else {},
                    is_active=bool(row['is_active']),
                    is_player_controlled=bool(row['is_player_controlled']),
                    created_at=row['created_at'],
                    updated_at=row['updated_at']
                )
        except Exception as e:
            print(f"加载NPC数据失败: {e}")
            return None

    def load_npcs_by_location(self, location_id: str) -> List[NPC]:
        """根据位置加载NPC列表"""
        try:
            with self.get_connection() as conn:
                rows = conn.execute('SELECT id FROM npcs WHERE current_location = ? AND is_active = 1', (location_id,)).fetchall()
                npcs = []
                for row in rows:
                    npc = self.load_npc(row['id'])
                    if npc:
                        npcs.append(npc)
                return npcs
        except Exception as e:
            print(f"根据位置加载NPC失败: {e}")
            return []

    def load_all_active_npcs(self) -> List[NPC]:
        """加载所有活跃的NPC"""
        try:
            with self.get_connection() as conn:
                rows = conn.execute('SELECT id FROM npcs WHERE is_active = 1').fetchall()
                npcs = []
                for row in rows:
                    npc = self.load_npc(row['id'])
                    if npc:
                        npcs.append(npc)
                return npcs
        except Exception as e:
            print(f"加载所有活跃NPC失败: {e}")
            return []

    def delete_world(self, world_id: str) -> bool:
        """删除世界及其所有相关数据"""
        try:
            with self.get_connection() as conn:
                # 删除相关数据（级联删除）
                conn.execute('DELETE FROM activities WHERE id IN (SELECT id FROM activities)')
                conn.execute('DELETE FROM jobs WHERE id IN (SELECT id FROM jobs)')
                conn.execute('DELETE FROM npcs WHERE id IN (SELECT id FROM npcs)')
                conn.execute('DELETE FROM organizations WHERE id IN (SELECT id FROM organizations)')
                conn.execute('DELETE FROM buildings WHERE id IN (SELECT id FROM buildings)')
                conn.execute('DELETE FROM blocks WHERE id IN (SELECT id FROM blocks)')
                conn.execute('DELETE FROM regions WHERE id IN (SELECT id FROM regions)')
                conn.execute('DELETE FROM worlds WHERE id = ?', (world_id,))
            return True
        except Exception as e:
            print(f"删除世界失败: {e}")
            return False
