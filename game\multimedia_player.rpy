# -*- coding: utf-8 -*-
# 多媒体播放系统界面

# 多媒体播放器主界面
screen multimedia_player_screen():
    tag menu
    
    # 背景
    add "#0a0a0a"
    
    # 标题栏
    frame:
        xalign 0.5
        ypos 10
        xsize 1000
        padding (20, 10)
        background "#1e1e1e"
        
        hbox:
            spacing 30
            text "多媒体中心" size 28 color "#ffffff"
            text "AI演出层体验" size 16 color "#888888"
    
    # 主要内容区域
    hbox:
        xalign 0.5
        ypos 70
        spacing 20
        
        # 左侧：NPC选择和控制
        frame:
            xsize 300
            ysize 600
            padding (15, 15)
            background "#2a2a2a"
            
            vbox:
                spacing 15
                
                text "NPC选择" size 20 color "#ffffff"
                
                # NPC列表
                viewport:
                    xsize 270
                    ysize 400
                    scrollbars "vertical"
                    mousewheel True
                    
                    vbox:
                        spacing 10
                        
                        if available_npcs_for_media:
                            for npc in available_npcs_for_media:
                                frame:
                                    padding (10, 8)
                                    background "#3a3a3a" if npc != selected_media_npc else "#4a90e2"
                                    
                                    hbox:
                                        spacing 10
                                        
                                        # NPC头像占位符
                                        frame:
                                            xsize 40
                                            ysize 40
                                            background "#555555"
                                            
                                            text "头像" size 8 color "#aaaaaa" xalign 0.5 yalign 0.5
                                        
                                        # NPC信息
                                        vbox:
                                            spacing 2
                                            textbutton "[npc.name]" action SetVariable("selected_media_npc", npc) text_size 14 color "#ffffff"
                                            text "[npc.age]岁 [npc.gender.value]" size 10 color "#aaaaaa"
                                            text "[npc.current_job.job_type.value if npc.current_job else '无业']" size 10 color "#aaaaaa"
                        else:
                            text "没有可用的NPC" size 14 color "#888888" xalign 0.5
                
                # 媒体生成控制
                text "媒体生成" size 16 color "#ffffff"
                
                hbox:
                    spacing 10
                    textbutton "生成语音" action Function(generate_npc_voice_demo) text_size 12
                    textbutton "生成肖像" action Function(generate_npc_portrait_demo) text_size 12
                
                # 播放控制
                text "播放控制" size 16 color "#ffffff"
                
                hbox:
                    spacing 10
                    textbutton "播放" action Function(play_current_media) text_size 12
                    textbutton "暂停" action Function(pause_current_media) text_size 12
                    textbutton "停止" action Function(stop_current_media) text_size 12
        
        # 中间：媒体展示区域
        frame:
            xsize 500
            ysize 600
            padding (20, 20)
            background "#2a2a2a"
            
            vbox:
                spacing 20
                
                text "媒体展示" size 24 color "#ffffff" xalign 0.5
                
                # 图像显示区域
                frame:
                    xsize 460
                    ysize 300
                    background "#1a1a1a"
                    
                    if current_display_image:
                        add current_display_image xalign 0.5 yalign 0.5
                    else:
                        text "暂无图像" size 16 color "#666666" xalign 0.5 yalign 0.5
                
                # 文本显示区域
                frame:
                    xsize 460
                    ysize 150
                    padding (15, 15)
                    background "#1a1a1a"
                    
                    viewport:
                        xsize 430
                        ysize 120
                        scrollbars "vertical"
                        mousewheel True
                        
                        text current_display_text size 14 color "#ffffff"
                
                # 音频控制条
                frame:
                    xsize 460
                    ysize 80
                    padding (15, 15)
                    background "#1a1a1a"
                    
                    vbox:
                        spacing 10
                        
                        text "音频播放" size 14 color "#ffffff"
                        
                        hbox:
                            spacing 10
                            
                            # 播放进度条
                            bar value audio_progress range 100 xsize 300 ysize 20 thumb None
                            text "[int(audio_progress)]%" size 12 color "#aaaaaa"
                        
                        hbox:
                            spacing 10
                            text "音量:" size 12 color "#aaaaaa"
                            bar value audio_volume range 100 xsize 200 ysize 15 thumb "gui/slider/horizontal_hover_thumb.png"
        
        # 右侧：任务状态和历史
        frame:
            xsize 300
            ysize 600
            padding (15, 15)
            background "#2a2a2a"
            
            vbox:
                spacing 15
                
                text "生成任务" size 20 color "#ffffff"
                
                # 当前任务状态
                frame:
                    padding (10, 10)
                    background "#3a3a3a"
                    
                    vbox:
                        spacing 8
                        text "当前任务状态" size 14 color "#ffffff"
                        
                        if current_generation_task:
                            text "任务ID: [current_generation_task.get('task_id', '')[:8]]..." size 10 color "#aaaaaa"
                            text "类型: [current_generation_task.get('media_type', '')]" size 10 color "#aaaaaa"
                            text "状态: [current_generation_task.get('status', '')]" size 10 color "#aaaaaa"
                            
                            # 进度条
                            bar value current_generation_task.get('progress', 0) * 100 range 100 xsize 250 ysize 15 thumb None
                            text "进度: [int(current_generation_task.get('progress', 0) * 100)]%" size 10 color "#aaaaaa"
                        else:
                            text "无活跃任务" size 12 color "#666666"
                
                # 任务历史
                text "任务历史" size 16 color "#ffffff"
                
                viewport:
                    xsize 270
                    ysize 350
                    scrollbars "vertical"
                    mousewheel True
                    
                    vbox:
                        spacing 5
                        
                        if generation_task_history:
                            for task in generation_task_history[-10:]:  # 显示最近10个任务
                                frame:
                                    padding (8, 6)
                                    background "#3a3a3a"
                                    
                                    vbox:
                                        spacing 3
                                        text "[task.get('media_type', '')] - [task.get('status', '')]" size 11 color "#ffffff"
                                        text "[task.get('created_at', '')[:16]]" size 9 color "#888888"
                                        
                                        if task.get('status') == 'completed' and task.get('result_asset_id'):
                                            textbutton "查看结果" action Function(view_generation_result, task.get('result_asset_id')) text_size 9
                        else:
                            text "暂无任务历史" size 12 color "#666666"
                
                # 系统状态
                text "系统状态" size 16 color "#ffffff"
                
                frame:
                    padding (10, 10)
                    background "#3a3a3a"
                    
                    vbox:
                        spacing 5
                        
                        if multimedia_system_status:
                            text "TTS服务: [multimedia_system_status.get('tts_status', {}).get('default_provider', '')]" size 10 color "#aaaaaa"
                            text "图像服务: [multimedia_system_status.get('image_status', {}).get('default_provider', '')]" size 10 color "#aaaaaa"
                            text "待处理任务: [multimedia_system_status.get('pending_tasks', 0)]" size 10 color "#aaaaaa"
                            text "缓存资源: [multimedia_system_status.get('cached_assets', 0)]" size 10 color "#aaaaaa"
                        else:
                            text "系统状态加载中..." size 10 color "#666666"
    
    # 底部控制栏
    frame:
        xalign 0.5
        ypos 680
        xsize 1000
        padding (20, 10)
        background "#1e1e1e"
        
        hbox:
            spacing 30
            
            textbutton "刷新数据" action Function(refresh_multimedia_data) text_size 14
            textbutton "清理缓存" action Function(clear_multimedia_cache) text_size 14
            textbutton "导出媒体" action Function(export_media_assets) text_size 14
            textbutton "设置" action Function(show_multimedia_settings) text_size 14
            textbutton "返回" action Jump("world_browser") text_size 14

# NPC媒体资源查看界面
screen npc_media_assets_screen():
    modal True
    
    # 半透明背景
    add "#000000aa"
    
    # 主面板
    frame:
        xalign 0.5
        yalign 0.5
        xsize 800
        ysize 600
        padding (30, 30)
        background "#2a2a2a"
        
        vbox:
            spacing 20
            
            # 标题
            if selected_media_npc:
                text "[selected_media_npc.name] 的媒体资源" size 24 color "#ffffff" xalign 0.5
            else:
                text "媒体资源" size 24 color "#ffffff" xalign 0.5
            
            # 资源列表
            viewport:
                xsize 740
                ysize 480
                scrollbars "vertical"
                mousewheel True
                
                vbox:
                    spacing 10
                    
                    if npc_media_assets:
                        for asset in npc_media_assets:
                            frame:
                                padding (15, 10)
                                background "#3a3a3a"
                                
                                hbox:
                                    spacing 20
                                    
                                    # 媒体类型图标
                                    frame:
                                        xsize 60
                                        ysize 60
                                        background "#555555"
                                        
                                        text asset.get('media_type', '') size 10 color "#ffffff" xalign 0.5 yalign 0.5
                                    
                                    # 资源信息
                                    vbox:
                                        spacing 5
                                        text "[asset.get('name', '')]" size 16 color "#ffffff"
                                        text "类型: [asset.get('media_type', '')] | 分类: [asset.get('category', '')]" size 12 color "#aaaaaa"
                                        text "创建时间: [asset.get('created_at', '')[:19]]" size 12 color "#aaaaaa"
                                        text "文件大小: [asset.get('file_size', 0)] 字节" size 12 color "#aaaaaa"
                                        
                                        if asset.get('duration', 0) > 0:
                                            text "时长: [asset.get('duration', 0):.1f] 秒" size 12 color "#aaaaaa"
                                    
                                    # 操作按钮
                                    vbox:
                                        spacing 5
                                        textbutton "播放" action Function(play_media_asset, asset.get('asset_id')) text_size 12
                                        textbutton "详情" action Function(show_asset_details, asset.get('asset_id')) text_size 12
                    else:
                        text "该NPC暂无媒体资源" size 16 color "#666666" xalign 0.5
            
            # 关闭按钮
            textbutton "关闭" action Return() text_size 16 xalign 0.5

# 全局变量和函数
init python:
    available_npcs_for_media = []
    selected_media_npc = None
    current_display_image = None
    current_display_text = "选择一个NPC开始体验AI演出功能..."
    audio_progress = 0
    audio_volume = 80
    current_generation_task = None
    generation_task_history = []
    multimedia_system_status = {}
    npc_media_assets = []
    multimedia_manager = None
    
    def init_multimedia_system():
        global multimedia_manager, db_manager
        if not db_manager:
            return False
        
        try:
            from core.multimedia_manager import MultimediaManager
            multimedia_manager = MultimediaManager("media")
            multimedia_manager.start_worker()
            return True
        except Exception as e:
            print(f"多媒体系统初始化失败: {e}")
            return False
    
    def refresh_multimedia_data():
        global available_npcs_for_media, multimedia_system_status
        
        if not multimedia_manager:
            if not init_multimedia_system():
                renpy.notify("多媒体系统初始化失败")
                return
        
        try:
            # 刷新NPC列表
            available_npcs_for_media = db_manager.load_all_active_npcs()[:10]  # 限制显示数量
            
            # 刷新系统状态
            multimedia_system_status = multimedia_manager.get_system_status()
            
            renpy.notify(f"已刷新，共{len(available_npcs_for_media)}个NPC")
            
        except Exception as e:
            renpy.notify(f"刷新失败: {e}")
    
    def generate_npc_voice_demo():
        global selected_media_npc, multimedia_manager, current_generation_task
        
        if not selected_media_npc or not multimedia_manager:
            renpy.notify("请先选择NPC并确保系统已初始化")
            return
        
        try:
            # 生成示例文本
            demo_texts = [
                f"你好，我是{selected_media_npc.name}。",
                f"今天天气不错，我感觉很好。",
                f"我今年{selected_media_npc.age}岁，很高兴认识你。",
                f"我的工作是{selected_media_npc.current_job.job_type.value if selected_media_npc.current_job else '暂时没有工作'}。"
            ]
            
            import random
            demo_text = random.choice(demo_texts)
            
            # 提交生成任务
            task_id = multimedia_manager.generate_npc_voice(selected_media_npc, demo_text, priority=3)
            
            # 更新当前任务状态
            current_generation_task = {
                "task_id": task_id,
                "media_type": "audio",
                "status": "pending",
                "progress": 0.0
            }
            
            renpy.notify(f"开始为{selected_media_npc.name}生成语音")
            
        except Exception as e:
            renpy.notify(f"语音生成失败: {e}")
    
    def generate_npc_portrait_demo():
        global selected_media_npc, multimedia_manager, current_generation_task
        
        if not selected_media_npc or not multimedia_manager:
            renpy.notify("请先选择NPC并确保系统已初始化")
            return
        
        try:
            from core.image_generation import ImageStyle
            
            # 提交生成任务
            task_id = multimedia_manager.generate_npc_portrait(selected_media_npc, ImageStyle.ANIME, priority=3)
            
            # 更新当前任务状态
            current_generation_task = {
                "task_id": task_id,
                "media_type": "image",
                "status": "pending",
                "progress": 0.0
            }
            
            renpy.notify(f"开始为{selected_media_npc.name}生成肖像")
            
        except Exception as e:
            renpy.notify(f"肖像生成失败: {e}")
    
    def check_generation_progress():
        global current_generation_task, generation_task_history, multimedia_manager
        
        if not current_generation_task or not multimedia_manager:
            return
        
        try:
            task_id = current_generation_task.get("task_id")
            if task_id:
                task_status = multimedia_manager.get_task_status(task_id)
                if task_status:
                    current_generation_task.update({
                        "status": task_status.status,
                        "progress": task_status.progress
                    })
                    
                    # 如果任务完成，添加到历史
                    if task_status.status in ["completed", "failed"]:
                        generation_task_history.append({
                            "task_id": task_id,
                            "media_type": task_status.media_type.value,
                            "status": task_status.status,
                            "created_at": task_status.created_at,
                            "result_asset_id": task_status.result_asset_id
                        })
                        
                        if task_status.status == "completed":
                            renpy.notify("生成完成！")
                        else:
                            renpy.notify(f"生成失败: {task_status.error_message}")
                        
                        current_generation_task = None
        
        except Exception as e:
            print(f"检查生成进度失败: {e}")
    
    def view_generation_result(asset_id):
        global multimedia_manager, current_display_image, current_display_text
        
        if not multimedia_manager:
            renpy.notify("系统未初始化")
            return
        
        try:
            asset = multimedia_manager.get_asset(asset_id)
            if asset:
                if asset.media_type.value == "image":
                    current_display_image = asset.file_path
                    current_display_text = f"图像: {asset.name}\n生成时间: {asset.created_at}\n提示词: {asset.metadata.get('prompt', '无')}"
                elif asset.media_type.value == "audio":
                    current_display_text = f"音频: {asset.name}\n生成时间: {asset.created_at}\n时长: {asset.duration:.1f}秒\n文本: {asset.metadata.get('text', '无')}"
                
                renpy.notify("已加载生成结果")
            else:
                renpy.notify("找不到资源")
        
        except Exception as e:
            renpy.notify(f"加载结果失败: {e}")
    
    def play_current_media():
        renpy.notify("播放功能开发中...")
    
    def pause_current_media():
        renpy.notify("暂停功能开发中...")
    
    def stop_current_media():
        renpy.notify("停止功能开发中...")
    
    def clear_multimedia_cache():
        global multimedia_manager
        
        if multimedia_manager:
            multimedia_manager.tts_manager.clear_cache()
            multimedia_manager.image_manager.clear_cache()
            renpy.notify("缓存已清理")
        else:
            renpy.notify("系统未初始化")
    
    def export_media_assets():
        renpy.notify("导出功能开发中...")
    
    def show_multimedia_settings():
        renpy.notify("设置功能开发中...")
    
    def play_media_asset(asset_id):
        renpy.notify(f"播放资源 {asset_id[:8]}... 功能开发中")
    
    def show_asset_details(asset_id):
        renpy.notify(f"资源详情 {asset_id[:8]}... 功能开发中")
    
    # 定时检查任务进度
    def multimedia_timer_callback():
        check_generation_progress()
        return 1.0  # 每秒检查一次
    
    # 启动定时器
    if config.developer:
        config.periodic_callbacks.append(multimedia_timer_callback)

# 多媒体中心入口
label multimedia_center:
    call init_system
    
    python:
        if not init_multimedia_system():
            renpy.notify("多媒体系统初始化失败")
            renpy.jump("world_browser")
        
        refresh_multimedia_data()
    
    call screen multimedia_player_screen
    
    return
