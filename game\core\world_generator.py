# -*- coding: utf-8 -*-
"""
世界生成器
负责生成初始的世界地图和行政区划结构
"""

import random
import math
from typing import List, Dict, Tuple
from datetime import datetime
from .data_models import (
    World, Region, Block, RegionType, generate_uuid
)
from .database import DatabaseManager


class WorldGenerator:
    """世界生成器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.world_names = [
            "阿尔法世界", "贝塔大陆", "伽马星球", "德尔塔领域", "艾普西隆大地",
            "泽塔星系", "伊塔世界", "西塔大陆", "约塔星球", "卡帕领域"
        ]
        self.country_names = [
            "东方联邦", "西部共和国", "北方王国", "南方帝国", "中央联盟",
            "海洋国度", "山脉王国", "森林共和国", "沙漠联邦", "冰原王国"
        ]
        self.province_names = [
            "青龙省", "白虎省", "朱雀省", "玄武省", "麒麟省",
            "凤凰省", "龙腾省", "虎跃省", "鹰翔省", "狼啸省"
        ]
        self.city_names = [
            "新月城", "星辰市", "晨曦镇", "暮光城", "黎明市",
            "夕阳城", "银河市", "彩虹镇", "雷鸣城", "风暴市"
        ]
        self.district_names = [
            "中心区", "商业区", "工业区", "住宅区", "文教区",
            "科技区", "金融区", "娱乐区", "港口区", "开发区"
        ]
        self.village_names = [
            "和谐村", "幸福村", "安宁村", "富裕村", "美丽村",
            "团结村", "进步村", "发展村", "繁荣村", "昌盛村"
        ]
    
    def generate_world(self, world_name: str = None, 
                      num_countries: int = 3,
                      provinces_per_country: Tuple[int, int] = (2, 4),
                      cities_per_province: Tuple[int, int] = (2, 5),
                      districts_per_city: Tuple[int, int] = (3, 6),
                      villages_per_city: Tuple[int, int] = (2, 4)) -> World:
        """
        生成完整的世界
        
        Args:
            world_name: 世界名称
            num_countries: 国家数量
            provinces_per_country: 每个国家的省份数量范围
            cities_per_province: 每个省份的城市数量范围
            districts_per_city: 每个城市的区数量范围
            villages_per_city: 每个城市的村数量范围
        """
        if not world_name:
            world_name = random.choice(self.world_names)
        
        # 创建世界实例
        world = World(
            id=generate_uuid(),
            name=world_name,
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
        
        # 生成国家
        countries = self._generate_countries(world.id, num_countries)
        total_regions = len(countries)
        total_blocks = 0
        total_population = 0
        
        # 为每个国家生成省份
        for i, country in enumerate(countries):
            country.id = f"{i+1:02d}"
            provinces = self._generate_provinces(
                world.id,
                country.id,
                random.randint(*provinces_per_country)
            )
            country.children_ids = [p.id for p in provinces]
            total_regions += len(provinces)
            
            # 为每个省份生成城市
            for j, province in enumerate(provinces):
                province.id = f"{country.id}-{j+1:02d}"
                province.parent_id = country.id
                cities = self._generate_cities(
                    world.id,
                    province.id,
                    random.randint(*cities_per_province)
                )
                province.children_ids = [c.id for c in cities]
                total_regions += len(cities)
                
                # 为每个城市生成区和村
                for k, city in enumerate(cities):
                    city.id = f"{province.id}-{k+1:02d}"
                    city.parent_id = province.id
                    
                    # 生成区
                    districts = self._generate_districts(
                        world.id,
                        city.id,
                        random.randint(*districts_per_city)
                    )
                    
                    # 生成村
                    villages = self._generate_villages(
                        world.id,
                        city.id,
                        random.randint(*villages_per_city)
                    )
                    
                    city.children_ids = [d.id for d in districts] + [v.id for v in villages]
                    total_blocks += len(districts) + len(villages)
                    
                    # 保存区块数据
                    for l, district in enumerate(districts):
                        district.id = f"{city.id}-{l+1:02d}"
                        district.parent_id = city.id
                        total_population += district.population
                        self.db.save_block(district)
                    
                    for l, village in enumerate(villages, start=len(districts)+1):
                        village.id = f"{city.id}-{l:02d}"
                        village.parent_id = city.id
                        total_population += village.population
                        self.db.save_block(village)
                    
                    # 更新城市人口统计
                    city.population = sum(d.population for d in districts) + sum(v.population for v in villages)
                    self.db.save_region(city)
                
                # 更新省份人口统计
                province.population = sum(c.population for c in cities)
                self.db.save_region(province)
            
            # 更新国家人口统计
            country.population = sum(p.population for p in provinces)
            self.db.save_region(country)
        
        # 更新世界统计信息
        world.total_regions = total_regions
        world.total_blocks = total_blocks
        world.total_population = total_population
        
        # 保存世界数据
        self.db.save_world(world)
        
        return world
    
    def _generate_countries(self, world_id: str, num_countries: int) -> List[Region]:
        """生成国家列表"""
        countries = []
        selected_names = random.sample(self.country_names, min(num_countries, len(self.country_names)))
        
        for i, name in enumerate(selected_names):
            country = Region(
                id="",  # 将在上级函数中设置
                name=name,
                type=RegionType.COUNTRY,
                world_id=world_id,
                gov_balance=random.uniform(1000000, 10000000),
                tax_rate=random.uniform(0.05, 0.25),
                infrastructure_score=random.uniform(40, 80),
                education_level=random.uniform(30, 70),
                healthcare_level=random.uniform(30, 70),
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat()
            )
            countries.append(country)
        
        return countries
    
    def _generate_provinces(self, world_id: str, country_id: str, num_provinces: int) -> List[Region]:
        """生成省份列表"""
        provinces = []
        selected_names = random.sample(self.province_names, min(num_provinces, len(self.province_names)))
        
        for name in selected_names:
            province = Region(
                id="",  # 将在上级函数中设置
                name=name,
                type=RegionType.PROVINCE,
                world_id=world_id,
                parent_id=country_id,
                gov_balance=random.uniform(100000, 1000000),
                tax_rate=random.uniform(0.03, 0.15),
                infrastructure_score=random.uniform(30, 70),
                education_level=random.uniform(25, 65),
                healthcare_level=random.uniform(25, 65),
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat()
            )
            provinces.append(province)
        
        return provinces
    
    def _generate_cities(self, world_id: str, province_id: str, num_cities: int) -> List[Region]:
        """生成城市列表"""
        cities = []
        selected_names = random.sample(self.city_names, min(num_cities, len(self.city_names)))
        
        for name in selected_names:
            city = Region(
                id="",  # 将在上级函数中设置
                name=name,
                type=RegionType.CITY,
                world_id=world_id,
                parent_id=province_id,
                gov_balance=random.uniform(10000, 100000),
                tax_rate=random.uniform(0.02, 0.10),
                infrastructure_score=random.uniform(20, 60),
                education_level=random.uniform(20, 60),
                healthcare_level=random.uniform(20, 60),
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat()
            )
            cities.append(city)
        
        return cities
    
    def _generate_districts(self, world_id: str, city_id: str, num_districts: int) -> List[Block]:
        """生成区列表"""
        districts = []
        selected_names = random.sample(self.district_names, min(num_districts, len(self.district_names)))
        
        for name in selected_names:
            land_area = random.uniform(10, 50)  # 平方公里
            buildable_index = random.uniform(100, 500)
            population = random.randint(10000, 100000)
            
            district = Block(
                id="",  # 将在上级函数中设置
                name=name,
                type=RegionType.DISTRICT,
                world_id=world_id,
                parent_id=city_id,
                land_area=land_area,
                buildable_index_max=buildable_index,
                buildable_index_used=random.uniform(buildable_index * 0.3, buildable_index * 0.8),
                population=population,
                population_cap=int(population * random.uniform(1.2, 2.0)),
                infrastructure_score=random.uniform(40, 80),
                land_price=random.uniform(5000, 20000),
                development_level=random.uniform(0.6, 1.5),
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat()
            )
            districts.append(district)
        
        return districts
    
    def _generate_villages(self, world_id: str, city_id: str, num_villages: int) -> List[Block]:
        """生成村列表"""
        villages = []
        selected_names = random.sample(self.village_names, min(num_villages, len(self.village_names)))
        
        for name in selected_names:
            land_area = random.uniform(5, 25)  # 平方公里
            buildable_index = random.uniform(20, 100)
            population = random.randint(1000, 10000)
            
            village = Block(
                id="",  # 将在上级函数中设置
                name=name,
                type=RegionType.VILLAGE,
                world_id=world_id,
                parent_id=city_id,
                land_area=land_area,
                buildable_index_max=buildable_index,
                buildable_index_used=random.uniform(buildable_index * 0.2, buildable_index * 0.6),
                population=population,
                population_cap=int(population * random.uniform(1.1, 1.8)),
                infrastructure_score=random.uniform(20, 50),
                land_price=random.uniform(1000, 5000),
                development_level=random.uniform(0.3, 1.0),
                created_at=datetime.now().isoformat(),
                updated_at=datetime.now().isoformat()
            )
            villages.append(village)
        
        return villages
