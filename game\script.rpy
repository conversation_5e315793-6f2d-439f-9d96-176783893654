# 最小化测试脚本
# 用于隔离和诊断问题

# 声明角色
define narrator = Character(None, kind=nvl)

# 使用default声明变量，确保在store中正确初始化
default core_loaded = False
default current_world = None
default db_manager = None
default world_generator = None
default init_success = False

# 最小化的初始化
init python:
    import sys
    import os
    
    global core_loaded, current_world, db_manager, world_generator, init_success

    print("开始最小化初始化...")

    # 添加game目录到Python路径
    try:
        game_path = os.path.join(config.gamedir)
        if game_path not in sys.path:
            sys.path.insert(0, game_path)
        print(f"已添加路径: {game_path}")
    except Exception as e:
        print(f"路径添加失败: {e}")

    # 尝试导入核心模块
    try:
        print("尝试导入核心模块...")
        from core import DatabaseManager
        print("DatabaseManager 导入成功")

        from core import WorldGenerator
        print("WorldGenerator 导入成功")

        from core import World, Region, Block
        print("数据模型导入成功")

        # 设置全局变量
        core_loaded = True
        print("所有核心模块导入成功")
        print(f"core_loaded 已设置为: {core_loaded}")

    except ImportError as e:
        print(f"导入错误: {e}")
        core_loaded = False
    except Exception as e:
        print(f"其他错误: {e}")
        core_loaded = False

# 游戏开始
label start:
    scene bg black
    
    "欢迎来到最小化测试版本！"
    
    python:
        print(f"core_loaded = {core_loaded}")
        print(f"current_world = {current_world}")
    
    # 直接检查模块是否可用，而不依赖变量
    python:
        try:
            from core import DatabaseManager
            modules_available = True
            print("✅ 模块检查：核心模块可用")
        except Exception as e:
            modules_available = False
            print(f"❌ 模块检查：核心模块不可用 - {e}")

    if modules_available:
        "✅ 核心系统加载成功！"
        
        python:
            global current_world, db_manager, world_generator, init_success
            try:
                print("尝试初始化数据库管理器...")
                db_manager = DatabaseManager()
                print("数据库管理器初始化成功")

                print("尝试初始化世界生成器...")
                world_generator = WorldGenerator(db_manager)
                print("世界生成器初始化成功")

                print("检查现有世界...")
                existing_worlds = db_manager.list_worlds()
                print(f"找到 {len(existing_worlds)} 个现有世界")

                if existing_worlds:
                    current_world = db_manager.load_world(existing_worlds[0]['id'])
                    print("加载了现有世界")
                else:
                    current_world = None
                    print("没有现有世界")

                init_success = True

            except Exception as e:
                print(f"初始化失败: {e}")
                import traceback
                traceback.print_exc()
                init_success = False
        
        if init_success:
            "✅ 系统初始化成功！"
            
            if current_world is None:
                "没有现有世界，将创建新世界..."
                jump create_test_world
            else:
                "找到现有世界，显示世界信息..."
                jump show_world_info
        else:
            "❌ 系统初始化失败，请查看控制台输出"
            return
    else:
        "❌ 核心模块不可用，请检查安装"
        python:
            print("核心模块检查失败，可能的原因：")
            print("1. 文件缺失或损坏")
            print("2. Python路径问题")
            print("3. 依赖模块问题")
        return

label create_test_world:
    "正在创建测试世界..."
    
    python:
        global current_world, world_generator
        try:
            print("开始创建世界...")
            current_world = world_generator.generate_world("测试世界")
            print(f"世界创建成功: {current_world.name}")
            world_created = True
        except Exception as e:
            print(f"世界创建失败: {e}")
            import traceback
            traceback.print_exc()
            world_created = False
    
    if world_created:
        "✅ 世界创建成功！"
        jump show_world_info
    else:
        "❌ 世界创建失败"
        return

label show_world_info:
    python:
        global current_world
        if current_world:
            world_name = current_world.name
            world_id = current_world.id
        else:
            world_name = "未知"
            world_id = "未知"
    
    "当前世界信息："
    "世界名称: [world_name]"
    "世界ID: [world_id]"
    
    menu:
        "选择下一步操作："
        
        "测试NPC生成":
            jump test_npc_generation
        
        "测试时间系统":
            jump test_time_system
        
        "退出测试":
            "测试完成，感谢使用！"
            return

label test_npc_generation:
    "正在测试NPC生成..."
    
    python:
        global current_world, db_manager
        try:
            from core import NPCGenerator
            npc_generator = NPCGenerator(db_manager)

            # 生成一个测试NPC
            test_npc = npc_generator.generate_npc()
            npc_name = test_npc.name
            npc_age = test_npc.age
            npc_gender = test_npc.gender.value
            npc_test_success = True
            
        except Exception as e:
            print(f"NPC生成失败: {e}")
            import traceback
            traceback.print_exc()
            npc_test_success = False
    
    if npc_test_success:
        "✅ NPC生成成功！"
        "生成的NPC信息："
        "姓名: [npc_name]"
        "年龄: [npc_age]岁"
        "性别: [npc_gender]"
    else:
        "❌ NPC生成失败"
    
    jump show_world_info

label test_time_system:
    "正在测试时间系统..."
    
    python:
        try:
            from core import TimeSystem
            time_system = TimeSystem(db_manager)
            
            current_time_info = time_system.get_current_time_info()
            time_str = current_time_info["time_string"]
            time_test_success = True
            
        except Exception as e:
            print(f"时间系统测试失败: {e}")
            import traceback
            traceback.print_exc()
            time_test_success = False
    
    if time_test_success:
        "✅ 时间系统正常！"
        "当前游戏时间: [time_str]"
    else:
        "❌ 时间系统失败"
    
    jump show_world_info
