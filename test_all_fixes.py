#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试所有修复 - 验证数据模型、NPC生成器、时间系统等
"""

import sys
import os

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

def test_data_models():
    """测试数据模型修复"""
    print("=== 测试数据模型修复 ===")
    
    try:
        from core.data_models import Region, Block, RegionType
        
        # 测试Region创建
        region = Region(
            id="01",
            name="测试国家",
            type=RegionType.COUNTRY,
            world_id="test-world-123"
        )
        print(f"✅ Region创建成功: {region.name}, world_id: {region.world_id}")
        
        # 测试Block创建
        block = Block(
            id="01-01-01-01",
            name="测试区块",
            type=RegionType.DISTRICT,
            world_id="test-world-123",
            parent_id="01-01-01",
            land_area=10.0,
            buildable_index_max=100.0
        )
        print(f"✅ Block创建成功: {block.name}, world_id: {block.world_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        return False

def test_world_generator():
    """测试世界生成器修复"""
    print("\n=== 测试世界生成器修复 ===")
    
    try:
        # 模拟Ren'Py环境
        import builtins
        original_import = builtins.__import__
        
        def mock_import(name, *args, **kwargs):
            if name == 'sqlite3':
                raise ImportError("No module named 'sqlite3'")
            return original_import(name, *args, **kwargs)
        
        builtins.__import__ = mock_import
        
        from core import DatabaseManager, WorldGenerator
        
        db_manager = DatabaseManager("test_fixes")
        world_generator = WorldGenerator(db_manager)
        
        # 生成小型测试世界
        world = world_generator.generate_world(
            world_name="修复测试世界",
            num_countries=1,
            provinces_per_country=(1, 1),
            cities_per_province=(1, 1),
            districts_per_city=(1, 1),
            villages_per_city=(1, 1)
        )
        
        print(f"✅ 世界生成成功: {world.name}")
        print(f"  - 总人口: {world.total_population:,}")
        print(f"  - 行政区域数: {world.total_regions}")
        print(f"  - 区块数: {world.total_blocks}")
        
        # 恢复导入
        builtins.__import__ = original_import
        
        # 清理测试数据
        import shutil
        if os.path.exists("test_fixes"):
            try:
                shutil.rmtree("test_fixes")
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ 世界生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_npc_generator():
    """测试NPC生成器修复"""
    print("\n=== 测试NPC生成器修复 ===")
    
    try:
        # 模拟Ren'Py环境
        import builtins
        original_import = builtins.__import__
        
        def mock_import(name, *args, **kwargs):
            if name == 'sqlite3':
                raise ImportError("No module named 'sqlite3'")
            return original_import(name, *args, **kwargs)
        
        builtins.__import__ = mock_import
        
        from core import DatabaseManager, NPCGenerator
        
        db_manager = DatabaseManager("test_npc")
        npc_generator = NPCGenerator(db_manager)
        
        # 生成测试NPC
        npc = npc_generator.generate_npc()
        print(f"✅ NPC生成成功: {npc.name}")
        print(f"  - 年龄: {npc.age}")
        print(f"  - 性别: {npc.gender.value}")
        print(f"  - 教育水平: {npc.education_level.value}")
        
        # 恢复导入
        builtins.__import__ = original_import
        
        # 清理测试数据
        import shutil
        if os.path.exists("test_npc"):
            try:
                shutil.rmtree("test_npc")
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ NPC生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_time_system():
    """测试时间系统修复"""
    print("\n=== 测试时间系统修复 ===")
    
    try:
        # 模拟Ren'Py环境
        import builtins
        original_import = builtins.__import__
        
        def mock_import(name, *args, **kwargs):
            if name == 'sqlite3':
                raise ImportError("No module named 'sqlite3'")
            return original_import(name, *args, **kwargs)
        
        builtins.__import__ = mock_import
        
        from core import DatabaseManager, TimeSystem
        
        db_manager = DatabaseManager("test_time")
        time_system = TimeSystem(db_manager)
        
        # 测试时间信息获取
        current_time_info = time_system.get_current_time_info()
        print(f"✅ 时间系统测试成功")
        print(f"  - 当前时间: {current_time_info['time_string']}")
        print(f"  - 天数: {current_time_info['day']}")
        print(f"  - 小时: {current_time_info['hour']}")
        
        # 恢复导入
        builtins.__import__ = original_import
        
        # 清理测试数据
        import shutil
        if os.path.exists("test_time"):
            try:
                shutil.rmtree("test_time")
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ 时间系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 所有修复验证测试")
    print("=" * 50)
    
    tests = [
        ("数据模型", test_data_models),
        ("世界生成器", test_world_generator),
        ("NPC生成器", test_npc_generator),
        ("时间系统", test_time_system)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有修复验证通过！")
        print("\n✨ 修复内容:")
        print("  - ✅ 数据模型添加了 world_id 属性")
        print("  - ✅ 世界生成器正确设置 world_id")
        print("  - ✅ NPC生成器参数调用修复")
        print("  - ✅ 时间系统方法名修复")
        print("  - ✅ 日期时间处理修复")
        
        print("\n🚀 游戏现在应该可以完全正常运行！")
        print("启动游戏后应该看到:")
        print("  1. ✅ 核心系统加载成功")
        print("  2. ✅ 系统初始化成功")
        print("  3. ✅ 世界创建成功")
        print("  4. 可以正常测试NPC生成和时间系统")
        
    else:
        print("\n❌ 部分修复验证失败")
        print("请检查失败的测试并进行修复")

if __name__ == "__main__":
    main()
