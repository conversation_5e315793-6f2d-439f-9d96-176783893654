#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终修复验证 - 测试直接模块检查方法
"""

import sys
import os

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

def test_direct_module_check():
    """测试直接模块检查方法"""
    print("=== 测试直接模块检查方法 ===")
    
    # 模拟Ren'Py环境（无sqlite3）
    import builtins
    original_import = builtins.__import__
    
    def mock_import(name, *args, **kwargs):
        if name == 'sqlite3':
            raise ImportError("No module named 'sqlite3'")
        return original_import(name, *args, **kwargs)
    
    builtins.__import__ = mock_import
    
    try:
        # 模拟游戏中的直接检查
        print("1. 尝试导入核心模块...")
        from core import DatabaseManager
        modules_available = True
        print("✅ 模块检查：核心模块可用")
        
        # 测试初始化
        print("2. 测试数据库管理器初始化...")
        db_manager = DatabaseManager()
        print("✅ 数据库管理器初始化成功")
        
        print("3. 测试世界生成器初始化...")
        from core import WorldGenerator
        world_generator = WorldGenerator(db_manager)
        print("✅ 世界生成器初始化成功")
        
        return True
        
    except Exception as e:
        modules_available = False
        print(f"❌ 模块检查：核心模块不可用 - {e}")
        return False
        
    finally:
        # 恢复原始导入函数
        builtins.__import__ = original_import

def main():
    """主测试函数"""
    print("🔧 最终修复验证")
    print("=" * 40)
    
    success = test_direct_module_check()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 最终修复验证成功！")
        print("\n✨ 修复要点:")
        print("  - 不再依赖变量传递")
        print("  - 直接在需要时检查模块可用性")
        print("  - 避免了Ren'Py变量作用域问题")
        
        print("\n🚀 现在启动游戏应该看到:")
        print("  1. '欢迎来到最小化测试版本！'")
        print("  2. '✅ 模块检查：核心模块可用'")
        print("  3. '✅ 核心系统加载成功！'")
        print("  4. '✅ 系统初始化成功！'")
        
        print("\n📝 启动方法:")
        print("  - 双击 start_game.bat")
        print("  - 或使用Ren'Py启动器")
        
    else:
        print("❌ 最终修复验证失败")
        print("需要进一步调试")

if __name__ == "__main__":
    main()
