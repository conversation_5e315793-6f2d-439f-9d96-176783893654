# -*- coding: utf-8 -*-
"""
市场事件系统
实现影响经济的随机事件，如通胀、经济危机、季节性需求变化等
"""

import random
import math
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from .data_models import generate_uuid


class EventType(Enum):
    """事件类型"""
    ECONOMIC_BOOM = "economic_boom"      # 经济繁荣
    ECONOMIC_CRISIS = "economic_crisis"  # 经济危机
    INFLATION = "inflation"              # 通胀
    DEFLATION = "deflation"              # 通缩
    SUPPLY_SHORTAGE = "supply_shortage"  # 供应短缺
    DEMAND_SURGE = "demand_surge"        # 需求激增
    SEASONAL_EVENT = "seasonal_event"    # 季节性事件
    TECHNOLOGY_BREAKTHROUGH = "tech_breakthrough"  # 技术突破
    NATURAL_DISASTER = "natural_disaster"  # 自然灾害
    POLICY_CHANGE = "policy_change"      # 政策变化


class EventSeverity(Enum):
    """事件严重程度"""
    MINOR = "minor"      # 轻微
    MODERATE = "moderate"  # 中等
    MAJOR = "major"      # 重大
    CRITICAL = "critical"  # 严重


@dataclass
class MarketEvent:
    """市场事件"""
    id: str
    name: str
    description: str
    event_type: EventType
    severity: EventSeverity
    affected_categories: List[str] = field(default_factory=list)  # 受影响的商品类别
    price_multiplier: float = 1.0  # 价格影响倍数
    supply_multiplier: float = 1.0  # 供应影响倍数
    demand_multiplier: float = 1.0  # 需求影响倍数
    duration_days: int = 7  # 持续天数
    probability: float = 0.1  # 发生概率
    created_at: str = ""
    expires_at: str = ""
    is_active: bool = True
    
    def get_impact_description(self) -> str:
        """获取影响描述"""
        impacts = []
        if self.price_multiplier != 1.0:
            change = "上涨" if self.price_multiplier > 1.0 else "下跌"
            percentage = abs((self.price_multiplier - 1.0) * 100)
            impacts.append(f"价格{change}{percentage:.1f}%")
        
        if self.supply_multiplier != 1.0:
            change = "增加" if self.supply_multiplier > 1.0 else "减少"
            percentage = abs((self.supply_multiplier - 1.0) * 100)
            impacts.append(f"供应{change}{percentage:.1f}%")
        
        if self.demand_multiplier != 1.0:
            change = "增加" if self.demand_multiplier > 1.0 else "减少"
            percentage = abs((self.demand_multiplier - 1.0) * 100)
            impacts.append(f"需求{change}{percentage:.1f}%")
        
        return "、".join(impacts) if impacts else "无明显影响"


class MarketEventSystem:
    """市场事件系统管理器"""
    
    def __init__(self):
        self.active_events: Dict[str, MarketEvent] = {}
        self.event_history: List[MarketEvent] = []
        self.event_templates = self._initialize_event_templates()
    
    def _initialize_event_templates(self) -> Dict[str, Dict[str, Any]]:
        """初始化事件模板"""
        return {
            "economic_boom": {
                "names": ["经济繁荣期", "市场回暖", "消费热潮"],
                "descriptions": [
                    "经济形势良好，消费者信心增强，市场活跃度提升",
                    "各行业表现强劲，就业率上升，购买力增强",
                    "投资增加，市场需求旺盛，价格稳中有升"
                ],
                "price_range": (1.05, 1.15),
                "supply_range": (1.0, 1.1),
                "demand_range": (1.1, 1.3),
                "duration_range": (14, 30),
                "probability": 0.15,
                "affected_categories": ["all"]
            },
            "economic_crisis": {
                "names": ["经济危机", "市场萧条", "消费低迷"],
                "descriptions": [
                    "经济下行压力增大，消费者信心不足，市场需求萎缩",
                    "失业率上升，收入减少，消费能力下降",
                    "企业经营困难，供应链受影响，价格波动加剧"
                ],
                "price_range": (0.8, 0.95),
                "supply_range": (0.8, 0.9),
                "demand_range": (0.7, 0.9),
                "duration_range": (21, 45),
                "probability": 0.08,
                "affected_categories": ["all"]
            },
            "supply_shortage_food": {
                "names": ["食品供应短缺", "农产品减产", "食品危机"],
                "descriptions": [
                    "由于天气异常，农产品产量下降，食品供应紧张",
                    "运输受阻，食品配送延迟，市场供应不足",
                    "食品安全问题导致部分产品下架，供应量减少"
                ],
                "price_range": (1.2, 1.5),
                "supply_range": (0.6, 0.8),
                "demand_range": (1.1, 1.3),
                "duration_range": (7, 21),
                "probability": 0.12,
                "affected_categories": ["food"]
            },
            "tech_breakthrough": {
                "names": ["技术突破", "科技创新", "数字化升级"],
                "descriptions": [
                    "新技术的应用提高了生产效率，降低了成本",
                    "数字化转型加速，智能设备需求激增",
                    "技术创新带来新的消费需求和市场机会"
                ],
                "price_range": (0.9, 1.1),
                "supply_range": (1.1, 1.3),
                "demand_range": (1.2, 1.4),
                "duration_range": (30, 60),
                "probability": 0.1,
                "affected_categories": ["technology", "education"]
            },
            "seasonal_spring": {
                "names": ["春季消费热潮", "春装上市", "春游旺季"],
                "descriptions": [
                    "春季到来，服装、娱乐消费需求增加",
                    "天气转暖，户外活动增多，相关商品热销",
                    "春节过后，消费市场逐渐回暖"
                ],
                "price_range": (1.05, 1.15),
                "supply_range": (1.0, 1.1),
                "demand_range": (1.1, 1.25),
                "duration_range": (30, 45),
                "probability": 0.8,  # 季节性事件概率较高
                "affected_categories": ["clothing", "entertainment", "transport"]
            },
            "health_crisis": {
                "names": ["健康危机", "医疗需求激增", "健康意识提升"],
                "descriptions": [
                    "公共健康事件导致医疗需求大幅增加",
                    "健康意识提升，保健品和医疗服务需求上升",
                    "医疗资源紧张，相关商品价格上涨"
                ],
                "price_range": (1.15, 1.4),
                "supply_range": (0.8, 1.0),
                "demand_range": (1.3, 1.6),
                "duration_range": (14, 30),
                "probability": 0.06,
                "affected_categories": ["health", "food"]
            }
        }
    
    def generate_random_event(self, current_season: str = "spring") -> Optional[MarketEvent]:
        """生成随机市场事件"""
        # 根据季节调整事件概率
        seasonal_events = {
            "spring": ["seasonal_spring", "tech_breakthrough"],
            "summer": ["supply_shortage_food", "economic_boom"],
            "autumn": ["economic_crisis", "health_crisis"],
            "winter": ["supply_shortage_food", "economic_boom"]
        }
        
        # 选择可能的事件类型
        possible_events = list(self.event_templates.keys())
        if current_season in seasonal_events:
            # 增加季节性事件的权重
            for seasonal_event in seasonal_events[current_season]:
                if seasonal_event in possible_events:
                    possible_events.extend([seasonal_event] * 3)  # 增加3倍权重
        
        # 随机选择事件类型
        event_type = random.choice(possible_events)
        template = self.event_templates[event_type]
        
        # 检查概率
        if random.random() > template["probability"]:
            return None
        
        # 生成事件
        event = MarketEvent(
            id=generate_uuid(),
            name=random.choice(template["names"]),
            description=random.choice(template["descriptions"]),
            event_type=EventType(event_type.split("_")[0] if "_" in event_type else event_type),
            severity=self._determine_severity(template),
            affected_categories=template["affected_categories"].copy(),
            price_multiplier=random.uniform(*template["price_range"]),
            supply_multiplier=random.uniform(*template["supply_range"]),
            demand_multiplier=random.uniform(*template["demand_range"]),
            duration_days=random.randint(*template["duration_range"]),
            probability=template["probability"],
            created_at=datetime.now().isoformat()
        )
        
        return event
    
    def _determine_severity(self, template: Dict[str, Any]) -> EventSeverity:
        """根据模板确定事件严重程度"""
        price_impact = abs(template["price_range"][1] - 1.0)
        supply_impact = abs(template["supply_range"][0] - 1.0)
        demand_impact = abs(template["demand_range"][1] - 1.0)
        
        max_impact = max(price_impact, supply_impact, demand_impact)
        
        if max_impact < 0.1:
            return EventSeverity.MINOR
        elif max_impact < 0.2:
            return EventSeverity.MODERATE
        elif max_impact < 0.4:
            return EventSeverity.MAJOR
        else:
            return EventSeverity.CRITICAL
    
    def activate_event(self, event: MarketEvent):
        """激活事件"""
        self.active_events[event.id] = event
        self.event_history.append(event)
    
    def get_active_events(self) -> List[MarketEvent]:
        """获取当前活跃事件"""
        return list(self.active_events.values())
    
    def get_category_multipliers(self, category: str) -> Tuple[float, float, float]:
        """获取特定类别的影响倍数 (price, supply, demand)"""
        price_mult = 1.0
        supply_mult = 1.0
        demand_mult = 1.0
        
        for event in self.active_events.values():
            if not event.is_active:
                continue
            
            if "all" in event.affected_categories or category in event.affected_categories:
                price_mult *= event.price_multiplier
                supply_mult *= event.supply_multiplier
                demand_mult *= event.demand_multiplier
        
        return price_mult, supply_mult, demand_mult
    
    def update_events(self, days_passed: int = 1):
        """更新事件状态"""
        expired_events = []
        
        for event_id, event in self.active_events.items():
            event.duration_days -= days_passed
            if event.duration_days <= 0:
                event.is_active = False
                expired_events.append(event_id)
        
        # 移除过期事件
        for event_id in expired_events:
            del self.active_events[event_id]
    
    def get_event_summary(self) -> Dict[str, Any]:
        """获取事件系统摘要"""
        active_events = list(self.active_events.values())
        
        return {
            "active_events_count": len(active_events),
            "total_events_history": len(self.event_history),
            "active_events": [
                {
                    "name": event.name,
                    "type": event.event_type.value,
                    "severity": event.severity.value,
                    "duration_remaining": event.duration_days,
                    "impact": event.get_impact_description(),
                    "affected_categories": event.affected_categories
                }
                for event in active_events
            ]
        }
