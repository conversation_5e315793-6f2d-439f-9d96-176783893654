#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整系统集成测试
测试M1-M5所有阶段的功能集成和协作
"""

import sys
import os
import random
import time
from datetime import datetime

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

def test_full_system_integration():
    """测试完整系统的集成功能"""
    print("=" * 80)
    print("完整系统集成测试：M1-M5所有阶段功能协作验证")
    print("=" * 80)
    
    try:
        # 导入所有核心模块
        from core import DatabaseManager, WorldGenerator, NPCGenerator
        from core.time_system import TimeSystem
        from core.player_system import PlayerSystem, PlayerMode
        from core.god_mode_editor import GodModeEditor
        from core.save_system import SaveSystem, SaveType
        from core.ai_event_generator import NewsSystem, EventContext
        from core.ai_service import ai_service_manager
        from core.multimedia_manager import MultimediaManager
        from core.image_generation import ImageStyle
        
        print("✓ 所有核心模块导入成功")
        
        # 1. 系统初始化（M1基础架构）
        print("\n1. 初始化完整系统...")
        db_manager = DatabaseManager("full_system_test.db")
        world_generator = WorldGenerator(db_manager)
        npc_generator = NPCGenerator(db_manager)
        time_system = TimeSystem(db_manager)
        player_system = PlayerSystem(db_manager)
        god_mode_editor = GodModeEditor(db_manager, time_system)
        save_system = SaveSystem(db_manager, "full_test_saves")
        news_system = NewsSystem(db_manager, ai_service_manager)
        multimedia_manager = MultimediaManager("full_test_media")
        
        print("✓ 完整系统初始化完成")
        
        # 2. 创建测试世界（M1世界生成）
        print("\n2. 创建测试世界...")
        world = world_generator.generate_world("完整系统测试世界")
        
        test_npcs = []
        for i in range(12):
            npc = npc_generator.generate_npc()
            npc.monthly_income = random.randint(2000, 10000)
            npc.cash = random.randint(500, 3000)
            test_npcs.append(npc)
            db_manager.save_npc(npc)
        
        print(f"✓ 创建了世界和 {len(test_npcs)} 个NPC")
        
        # 3. 经济系统运行（M2经济交互）
        print("\n3. 测试经济系统...")
        
        # 推进时间，触发经济活动
        for i in range(3):
            print(f"  经济周期 {i+1}:")
            results = time_system.advance_time(8)  # 推进8小时
            
            if results.get("economic_updates"):
                print(f"    经济更新: {len(results['economic_updates'])} 项")
            if results.get("completed_activities"):
                print(f"    完成活动: {len(results['completed_activities'])} 个")
        
        economic_summary = time_system.economy_system.get_economic_summary()
        print(f"  经济状态: 总现金¥{economic_summary['total_cash']:,.0f}, 交易{economic_summary['daily_transactions']}笔")
        
        # 4. AI新闻生成（M3事件流）
        print("\n4. 测试AI新闻生成...")
        
        # 构建事件上下文
        context = EventContext(
            world_state={"total_population": len(test_npcs)},
            recent_events=[],
            active_npcs=test_npcs,
            economic_data=economic_summary,
            time_info=time_system.get_current_time_info(),
            market_events=list(time_system.market_event_system.active_events.values())
        )
        
        # 生成新闻
        daily_news = news_system.generate_daily_news(context)
        print(f"  生成新闻: {len(daily_news)} 条")
        
        for i, news in enumerate(daily_news[:2]):
            print(f"    新闻{i+1}: {news.title}")
            print(f"      内容: {news.content[:50]}...")
        
        # 5. 玩家系统测试（M4角色切换）
        print("\n5. 测试玩家系统...")
        
        # 测试角色模式
        test_character = test_npcs[0]
        success = player_system.switch_to_character_mode(test_character.id)
        print(f"  切换到角色模式: {'成功' if success else '失败'}")
        
        if success:
            current_char = player_system.get_current_character()
            print(f"  当前角色: {current_char.name}")
            
            # 记录一些角色行为
            player_system.record_action("work", {"description": "努力工作"})
            player_system.record_action("socialize", {"description": "与朋友聊天", "memorable": True})
        
        # 切换到上帝模式
        player_system.switch_to_god_mode()
        print("  切换到上帝模式: 成功")
        
        # 6. 上帝模式编辑（M4世界编辑）
        print("\n6. 测试上帝模式编辑...")
        
        # 编辑NPC
        target_npc = test_npcs[1]
        original_cash = target_npc.cash
        
        changes = {
            "cash": original_cash + 2000,
            "daily_stats": {"happiness": 85, "energy": 90}
        }
        
        success, message = god_mode_editor.edit_npc(target_npc.id, changes)
        print(f"  NPC编辑: {message}")
        
        if success:
            updated_npc = db_manager.load_npc(target_npc.id)
            print(f"    现金变化: ¥{original_cash:.0f} → ¥{updated_npc.cash:.0f}")
        
        # 7. 存档系统测试（M4数据管理）
        print("\n7. 测试存档系统...")
        
        # 创建存档
        success, message = save_system.create_save("集成测试存档", SaveType.MANUAL, "完整系统功能测试")
        print(f"  创建存档: {message}")
        
        # 获取存档列表
        saves = save_system.get_save_list()
        print(f"  存档数量: {len(saves)}")
        
        if saves:
            latest_save = saves[0]
            print(f"    最新存档: {latest_save.save_name}")
            print(f"    NPC数量: {latest_save.npc_count}, 总现金: ¥{latest_save.total_cash:.0f}")
        
        # 8. 多媒体系统测试（M5演出层）
        print("\n8. 测试多媒体系统...")
        
        multimedia_manager.start_worker()
        
        # 为几个NPC生成多媒体内容
        media_tasks = []
        
        for npc in test_npcs[:3]:
            # 生成语音
            voice_task = multimedia_manager.generate_npc_voice(
                npc, 
                f"你好，我是{npc.name}，这是系统集成测试。", 
                priority=3
            )
            media_tasks.append(voice_task)
            
            # 生成肖像
            portrait_task = multimedia_manager.generate_npc_portrait(
                npc, 
                ImageStyle.ANIME, 
                priority=3
            )
            media_tasks.append(portrait_task)
        
        print(f"  提交多媒体任务: {len(media_tasks)} 个")
        
        # 等待部分任务完成
        completed = 0
        wait_time = 0
        while completed < len(media_tasks) // 2 and wait_time < 15:
            for task_id in media_tasks:
                task_status = multimedia_manager.get_task_status(task_id)
                if task_status and task_status.status == "completed":
                    completed += 1
            time.sleep(0.5)
            wait_time += 0.5
        
        print(f"  完成任务: {completed} 个")
        
        # 9. 系统协作测试
        print("\n9. 测试系统协作...")
        
        # 时间推进 → 经济更新 → 新闻生成 → 多媒体内容
        print("  执行完整的系统循环...")
        
        # 推进时间
        time_results = time_system.advance_time(12)
        print(f"    时间推进: 完成{len(time_results.get('completed_activities', []))}个活动")
        
        # 检查新闻生成
        if time_results.get("news_generated"):
            print(f"    自动生成新闻: {len(time_results['news_generated'])} 条")
        
        # 检查经济影响
        if time_results.get("economic_updates"):
            print(f"    经济系统更新: {len(time_results['economic_updates'])} 项")
        
        # 10. 性能和稳定性测试
        print("\n10. 性能和稳定性测试...")
        
        start_time = time.time()
        
        # 批量操作测试
        batch_operations = 0
        
        # 批量NPC编辑
        batch_changes = {"daily_stats": {"energy": 75}}
        npc_ids = [npc.id for npc in test_npcs[:5]]
        success_count, errors = god_mode_editor.batch_edit_npcs(npc_ids, batch_changes)
        batch_operations += success_count
        
        # 批量新闻生成
        for _ in range(3):
            news = news_system.generate_daily_news(context)
            batch_operations += len(news)
        
        total_time = time.time() - start_time
        
        print(f"  批量操作: {batch_operations} 个操作")
        print(f"  总耗时: {total_time:.3f} 秒")
        print(f"  平均每操作: {total_time/batch_operations:.3f} 秒")
        
        # 11. 最终状态报告
        print("\n11. 最终系统状态报告...")
        
        # 收集所有系统状态
        final_economic = time_system.economy_system.get_economic_summary()
        final_player = player_system.get_player_stats()
        final_news = news_system.get_news_summary()
        final_multimedia = multimedia_manager.get_system_status()
        final_saves = save_system.get_save_statistics()
        
        print("  系统状态摘要:")
        print(f"    世界: {world.name}")
        print(f"    NPC数量: {len(test_npcs)}")
        print(f"    当前时间: {time_system.get_current_time_info()['time_string']}")
        print(f"    经济状态: 总现金¥{final_economic['total_cash']:,.0f}, 交易{final_economic['daily_transactions']}笔")
        print(f"    玩家模式: {final_player['current_mode']}, 操作{final_player['actions_taken']}次")
        print(f"    新闻系统: 总计{final_news['total_articles']}条, 最近{final_news['recent_articles']}条")
        print(f"    多媒体: 任务{final_multimedia['total_tasks']}个, 缓存{final_multimedia['cached_assets']}项")
        print(f"    存档系统: {final_saves['total_saves']}个存档, 总大小{final_saves['total_size']}字节")
        
        # 清理资源
        multimedia_manager.stop_worker()
        
        print("\n" + "=" * 80)
        print("✅ 完整系统集成测试成功完成！")
        print("✅ M1基础架构: 数据模型、世界生成、NPC生成 ✓")
        print("✅ M2经济系统: 多NPC交互、动态价格、市场事件 ✓")
        print("✅ M3事件流: AI新闻生成、内容过滤、故事线 ✓")
        print("✅ M4玩家系统: 角色切换、上帝模式、存档管理 ✓")
        print("✅ M5演出层: TTS语音、文生图、多媒体管理 ✓")
        print("✅ 系统协作: 所有模块协同工作，性能稳定 ✓")
        print("=" * 80)
        print("🎉 AI驱动沙盒游戏系统开发完成！")
        print("🎉 这是一个真正'活着'的虚拟世界！")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_system_integration()
    sys.exit(0 if success else 1)
