# -*- coding: utf-8 -*-
"""
AI服务接口抽象层
支持多种AI服务提供商（OpenAI、<PERSON>、<PERSON>等）
"""

import json
import random
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod


class AIProvider(Enum):
    """AI服务提供商"""
    OPENAI = "openai"
    GEMINI = "gemini"
    CLAUDE = "claude"
    LOCAL = "local"  # 本地模拟
    MOCK = "mock"    # 测试模拟


@dataclass
class AIRequest:
    """AI请求"""
    prompt: str
    system_prompt: str = ""
    max_tokens: int = 500
    temperature: float = 0.7
    context: Dict[str, Any] = field(default_factory=dict)
    request_type: str = "general"  # general, news, event, story


@dataclass
class AIResponse:
    """AI响应"""
    content: str
    success: bool = True
    error_message: str = ""
    usage_info: Dict[str, Any] = field(default_factory=dict)
    provider: str = ""
    request_id: str = ""


class AIServiceInterface(ABC):
    """AI服务接口"""
    
    @abstractmethod
    def generate_text(self, request: AIRequest) -> AIResponse:
        """生成文本"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查服务是否可用"""
        pass
    
    @abstractmethod
    def get_provider_name(self) -> str:
        """获取提供商名称"""
        pass


class MockAIService(AIServiceInterface):
    """模拟AI服务（用于测试和离线使用）"""
    
    def __init__(self):
        self.news_templates = {
            "economic": [
                "市场价格波动引起消费者关注，{product}价格{change}了{percentage}%",
                "经济专家分析，当前{category}市场呈现{trend}趋势",
                "本地商家报告，{product}销量{change_type}，预计将影响未来价格走势"
            ],
            "social": [
                "{npc_name}在{location}的行为引起了当地居民的讨论",
                "社区活动增加，居民幸福指数有所提升",
                "最近的社会调查显示，{percentage}%的居民对当前生活状况表示满意"
            ],
            "weather": [
                "天气变化影响了农产品供应，预计{category}价格将有所调整",
                "季节性需求变化导致{category}商品热销",
                "气候条件良好，有利于各类商品的生产和流通"
            ],
            "technology": [
                "新技术的应用提高了生产效率，{category}成本有所降低",
                "数字化转型加速，智能设备需求持续增长",
                "技术创新为{category}行业带来新的发展机遇"
            ]
        }
        
        self.event_templates = {
            "festival": [
                "一年一度的{season}节庆活动即将开始，预计将带动相关消费",
                "传统节日临近，{category}商品需求量显著增加",
                "节庆氛围浓厚，各类娱乐活动备受欢迎"
            ],
            "discovery": [
                "在{location}发现了有趣的现象，引起了研究人员的关注",
                "新的发现可能会改变人们对{category}的认知",
                "科学研究取得突破，为{category}发展提供新思路"
            ],
            "community": [
                "社区居民自发组织了{activity}活动，促进了邻里关系",
                "当地政府宣布了新的{policy}政策，预计将影响{category}",
                "居民代表提出了关于{topic}的建议，得到了积极响应"
            ]
        }
    
    def generate_text(self, request: AIRequest) -> AIResponse:
        """生成模拟文本"""
        try:
            content = self._generate_mock_content(request)
            return AIResponse(
                content=content,
                success=True,
                provider="mock",
                request_id=f"mock_{random.randint(1000, 9999)}"
            )
        except Exception as e:
            return AIResponse(
                content="",
                success=False,
                error_message=str(e),
                provider="mock"
            )
    
    def _generate_mock_content(self, request: AIRequest) -> str:
        """生成模拟内容"""
        context = request.context
        request_type = request.request_type
        
        if request_type == "news":
            return self._generate_mock_news(context)
        elif request_type == "event":
            return self._generate_mock_event(context)
        elif request_type == "story":
            return self._generate_mock_story(context)
        else:
            return self._generate_general_response(request.prompt, context)
    
    def _generate_mock_news(self, context: Dict[str, Any]) -> str:
        """生成模拟新闻"""
        news_type = context.get("news_type", "economic")
        templates = self.news_templates.get(news_type, self.news_templates["economic"])
        
        template = random.choice(templates)
        
        # 填充模板变量
        replacements = {
            "product": context.get("product", "商品"),
            "category": context.get("category", "市场"),
            "change": "上涨" if random.random() > 0.5 else "下跌",
            "percentage": str(random.randint(5, 25)),
            "trend": "上升" if random.random() > 0.5 else "下降",
            "change_type": "增加" if random.random() > 0.5 else "减少",
            "npc_name": context.get("npc_name", "某位居民"),
            "location": context.get("location", "市中心"),
            "season": context.get("season", "春季")
        }
        
        for key, value in replacements.items():
            template = template.replace(f"{{{key}}}", str(value))
        
        return template
    
    def _generate_mock_event(self, context: Dict[str, Any]) -> str:
        """生成模拟事件"""
        event_type = context.get("event_type", "community")
        templates = self.event_templates.get(event_type, self.event_templates["community"])
        
        template = random.choice(templates)
        
        replacements = {
            "location": context.get("location", "当地"),
            "category": context.get("category", "相关"),
            "activity": context.get("activity", "公益"),
            "policy": context.get("policy", "民生"),
            "topic": context.get("topic", "社区发展"),
            "season": context.get("season", "春季")
        }
        
        for key, value in replacements.items():
            template = template.replace(f"{{{key}}}", str(value))
        
        return template
    
    def _generate_mock_story(self, context: Dict[str, Any]) -> str:
        """生成模拟故事"""
        character = context.get("character", "主角")
        setting = context.get("setting", "城市")
        theme = context.get("theme", "日常生活")
        
        story_templates = [
            f"{character}在{setting}的{theme}中遇到了有趣的挑战，通过努力最终获得了成长。",
            f"在{setting}，{character}的{theme}故事展现了人性的美好和坚韧。",
            f"{character}在{setting}经历了一段关于{theme}的难忘旅程。"
        ]
        
        return random.choice(story_templates)
    
    def _generate_general_response(self, prompt: str, context: Dict[str, Any]) -> str:
        """生成通用响应"""
        responses = [
            f"根据当前情况分析，{prompt}的相关因素需要进一步观察。",
            f"关于{prompt}，系统建议采取谨慎的态度进行处理。",
            f"经过综合考虑，{prompt}可能会对当前状况产生一定影响。"
        ]
        
        return random.choice(responses)
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return True
    
    def get_provider_name(self) -> str:
        """获取提供商名称"""
        return "Mock AI Service"


class AIServiceManager:
    """AI服务管理器"""
    
    def __init__(self):
        self.services: Dict[AIProvider, AIServiceInterface] = {}
        self.default_provider = AIProvider.MOCK
        self.fallback_provider = AIProvider.MOCK
        
        # 初始化默认服务
        self.services[AIProvider.MOCK] = MockAIService()
    
    def register_service(self, provider: AIProvider, service: AIServiceInterface):
        """注册AI服务"""
        self.services[provider] = service
    
    def set_default_provider(self, provider: AIProvider):
        """设置默认提供商"""
        if provider in self.services:
            self.default_provider = provider
    
    def generate_text(self, request: AIRequest, provider: Optional[AIProvider] = None) -> AIResponse:
        """生成文本（带故障转移）"""
        target_provider = provider or self.default_provider
        
        # 尝试使用指定的提供商
        if target_provider in self.services:
            service = self.services[target_provider]
            if service.is_available():
                response = service.generate_text(request)
                if response.success:
                    return response
        
        # 故障转移到备用提供商
        if self.fallback_provider in self.services and self.fallback_provider != target_provider:
            fallback_service = self.services[self.fallback_provider]
            if fallback_service.is_available():
                response = fallback_service.generate_text(request)
                response.provider += " (fallback)"
                return response
        
        # 所有服务都不可用
        return AIResponse(
            content="AI服务暂时不可用，请稍后再试。",
            success=False,
            error_message="All AI services unavailable",
            provider="none"
        )
    
    def get_available_providers(self) -> List[AIProvider]:
        """获取可用的提供商列表"""
        available = []
        for provider, service in self.services.items():
            if service.is_available():
                available.append(provider)
        return available
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        status = {
            "default_provider": self.default_provider.value,
            "fallback_provider": self.fallback_provider.value,
            "services": {}
        }
        
        for provider, service in self.services.items():
            status["services"][provider.value] = {
                "available": service.is_available(),
                "name": service.get_provider_name()
            }
        
        return status


# 全局AI服务管理器实例
ai_service_manager = AIServiceManager()
