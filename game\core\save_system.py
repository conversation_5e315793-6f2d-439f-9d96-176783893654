# -*- coding: utf-8 -*-
"""
存档管理系统
支持多存档、快照、回滚、导入导出等功能
"""

import json
import os
import shutil
import zipfile
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
from datetime import datetime
import sqlite3

from .database import DatabaseManager


class SaveType(Enum):
    """存档类型"""
    MANUAL = "manual"        # 手动存档
    AUTO = "auto"           # 自动存档
    CHECKPOINT = "checkpoint"  # 检查点
    SNAPSHOT = "snapshot"    # 快照


@dataclass
class SaveMetadata:
    """存档元数据"""
    save_id: str
    save_name: str
    save_type: SaveType
    created_at: str
    world_name: str
    current_day: int
    current_season: str
    npc_count: int
    total_cash: float
    player_mode: str
    description: str = ""
    file_size: int = 0
    version: str = "1.0"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['save_type'] = self.save_type.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SaveMetadata':
        """从字典创建"""
        data['save_type'] = SaveType(data['save_type'])
        return cls(**data)


class SaveSystem:
    """存档系统管理器"""
    
    def __init__(self, db_manager: DatabaseManager, save_directory: str = "saves"):
        self.db = db_manager
        self.save_directory = save_directory
        self.max_auto_saves = 10
        self.max_snapshots = 20
        
        # 确保存档目录存在
        os.makedirs(save_directory, exist_ok=True)
        
        # 初始化存档索引数据库
        self.index_db_path = os.path.join(save_directory, "save_index.db")
        self._init_index_database()
    
    def _init_index_database(self):
        """初始化存档索引数据库"""
        conn = sqlite3.connect(self.index_db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS save_metadata (
                save_id TEXT PRIMARY KEY,
                save_name TEXT NOT NULL,
                save_type TEXT NOT NULL,
                created_at TEXT NOT NULL,
                world_name TEXT,
                current_day INTEGER,
                current_season TEXT,
                npc_count INTEGER,
                total_cash REAL,
                player_mode TEXT,
                description TEXT,
                file_size INTEGER,
                version TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def create_save(self, save_name: str, save_type: SaveType = SaveType.MANUAL, 
                   description: str = "") -> Tuple[bool, str]:
        """创建存档"""
        try:
            from .data_models import generate_uuid
            from .time_system import TimeSystem
            from .player_system import PlayerSystem
            
            save_id = generate_uuid()
            timestamp = datetime.now().isoformat()
            
            # 获取当前世界状态
            time_system = TimeSystem(self.db)
            player_system = PlayerSystem(self.db)
            
            current_time = time_system.get_current_time_info()
            economic_summary = time_system.economy_system.get_economic_summary()
            player_stats = player_system.get_player_stats()
            
            # 创建存档目录
            save_path = os.path.join(self.save_directory, save_id)
            os.makedirs(save_path, exist_ok=True)
            
            # 复制数据库文件
            db_backup_path = os.path.join(save_path, "game_data.db")
            shutil.copy2(self.db.db_path, db_backup_path)
            
            # 保存系统状态
            system_state = {
                "time_system": {
                    "current_day": time_system.current_day,
                    "current_hour": time_system.current_hour,
                    "current_season": time_system.current_season,
                    "current_year": time_system.current_year
                },
                "economy_system": {
                    "current_season": time_system.economy_system.current_season,
                    "daily_transaction_count": time_system.economy_system.daily_transaction_count
                },
                "player_system": player_stats,
                "market_events": [
                    {
                        "id": event.id,
                        "name": event.name,
                        "description": event.description,
                        "duration_days": event.duration_days,
                        "is_active": event.is_active
                    }
                    for event in time_system.market_event_system.active_events.values()
                ]
            }
            
            system_state_path = os.path.join(save_path, "system_state.json")
            with open(system_state_path, 'w', encoding='utf-8') as f:
                json.dump(system_state, f, ensure_ascii=False, indent=2)
            
            # 计算文件大小
            file_size = self._calculate_directory_size(save_path)
            
            # 创建存档元数据
            metadata = SaveMetadata(
                save_id=save_id,
                save_name=save_name,
                save_type=save_type,
                created_at=timestamp,
                world_name=current_time.get('world_name', 'Unknown World'),
                current_day=current_time.get('day', 1),
                current_season=current_time.get('season', 'spring'),
                npc_count=economic_summary.get('total_npcs', 0),
                total_cash=economic_summary.get('total_cash', 0.0),
                player_mode=player_stats.get('current_mode', 'god_mode'),
                description=description,
                file_size=file_size
            )
            
            # 保存元数据到索引数据库
            self._save_metadata(metadata)
            
            # 保存元数据到存档目录
            metadata_path = os.path.join(save_path, "metadata.json")
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata.to_dict(), f, ensure_ascii=False, indent=2)
            
            # 清理旧的自动存档
            if save_type == SaveType.AUTO:
                self._cleanup_old_saves(SaveType.AUTO, self.max_auto_saves)
            elif save_type == SaveType.SNAPSHOT:
                self._cleanup_old_saves(SaveType.SNAPSHOT, self.max_snapshots)
            
            return True, f"存档创建成功: {save_name} (ID: {save_id})"
            
        except Exception as e:
            return False, f"创建存档失败: {e}"
    
    def load_save(self, save_id: str) -> Tuple[bool, str]:
        """加载存档"""
        try:
            # 检查存档是否存在
            save_path = os.path.join(self.save_directory, save_id)
            if not os.path.exists(save_path):
                return False, "存档不存在"
            
            # 加载元数据
            metadata_path = os.path.join(save_path, "metadata.json")
            if not os.path.exists(metadata_path):
                return False, "存档元数据缺失"
            
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata_dict = json.load(f)
            
            metadata = SaveMetadata.from_dict(metadata_dict)
            
            # 备份当前数据库
            backup_path = self.db.db_path + ".backup"
            shutil.copy2(self.db.db_path, backup_path)
            
            try:
                # 恢复数据库
                db_backup_path = os.path.join(save_path, "game_data.db")
                if os.path.exists(db_backup_path):
                    shutil.copy2(db_backup_path, self.db.db_path)
                
                # 恢复系统状态
                system_state_path = os.path.join(save_path, "system_state.json")
                if os.path.exists(system_state_path):
                    with open(system_state_path, 'r', encoding='utf-8') as f:
                        system_state = json.load(f)
                    
                    # 这里需要重新初始化系统状态
                    # 由于系统复杂性，这里简化处理
                    pass
                
                return True, f"存档加载成功: {metadata.save_name}"
                
            except Exception as e:
                # 恢复失败，回滚数据库
                shutil.copy2(backup_path, self.db.db_path)
                return False, f"存档加载失败，已回滚: {e}"
            
        except Exception as e:
            return False, f"加载存档失败: {e}"
    
    def delete_save(self, save_id: str) -> Tuple[bool, str]:
        """删除存档"""
        try:
            # 检查存档是否存在
            save_path = os.path.join(self.save_directory, save_id)
            if not os.path.exists(save_path):
                return False, "存档不存在"
            
            # 获取存档信息
            metadata = self.get_save_metadata(save_id)
            save_name = metadata.save_name if metadata else save_id
            
            # 删除存档目录
            shutil.rmtree(save_path)
            
            # 从索引数据库中删除
            conn = sqlite3.connect(self.index_db_path)
            cursor = conn.cursor()
            cursor.execute("DELETE FROM save_metadata WHERE save_id = ?", (save_id,))
            conn.commit()
            conn.close()
            
            return True, f"存档删除成功: {save_name}"
            
        except Exception as e:
            return False, f"删除存档失败: {e}"
    
    def get_save_list(self, save_type: Optional[SaveType] = None) -> List[SaveMetadata]:
        """获取存档列表"""
        try:
            conn = sqlite3.connect(self.index_db_path)
            cursor = conn.cursor()
            
            if save_type:
                cursor.execute(
                    "SELECT * FROM save_metadata WHERE save_type = ? ORDER BY created_at DESC",
                    (save_type.value,)
                )
            else:
                cursor.execute("SELECT * FROM save_metadata ORDER BY created_at DESC")
            
            saves = []
            for row in cursor.fetchall():
                metadata_dict = {
                    'save_id': row[0],
                    'save_name': row[1],
                    'save_type': row[2],
                    'created_at': row[3],
                    'world_name': row[4],
                    'current_day': row[5],
                    'current_season': row[6],
                    'npc_count': row[7],
                    'total_cash': row[8],
                    'player_mode': row[9],
                    'description': row[10],
                    'file_size': row[11],
                    'version': row[12]
                }
                saves.append(SaveMetadata.from_dict(metadata_dict))
            
            conn.close()
            return saves
            
        except Exception as e:
            print(f"获取存档列表失败: {e}")
            return []
    
    def get_save_metadata(self, save_id: str) -> Optional[SaveMetadata]:
        """获取存档元数据"""
        try:
            conn = sqlite3.connect(self.index_db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM save_metadata WHERE save_id = ?", (save_id,))
            row = cursor.fetchone()
            conn.close()
            
            if row:
                metadata_dict = {
                    'save_id': row[0],
                    'save_name': row[1],
                    'save_type': row[2],
                    'created_at': row[3],
                    'world_name': row[4],
                    'current_day': row[5],
                    'current_season': row[6],
                    'npc_count': row[7],
                    'total_cash': row[8],
                    'player_mode': row[9],
                    'description': row[10],
                    'file_size': row[11],
                    'version': row[12]
                }
                return SaveMetadata.from_dict(metadata_dict)
            
            return None
            
        except Exception as e:
            print(f"获取存档元数据失败: {e}")
            return None
    
    def export_save(self, save_id: str, export_path: str) -> Tuple[bool, str]:
        """导出存档"""
        try:
            save_path = os.path.join(self.save_directory, save_id)
            if not os.path.exists(save_path):
                return False, "存档不存在"
            
            # 创建ZIP文件
            with zipfile.ZipFile(export_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(save_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, save_path)
                        zipf.write(file_path, arcname)
            
            return True, f"存档导出成功: {export_path}"
            
        except Exception as e:
            return False, f"导出存档失败: {e}"
    
    def import_save(self, import_path: str) -> Tuple[bool, str]:
        """导入存档"""
        try:
            if not os.path.exists(import_path):
                return False, "导入文件不存在"
            
            from .data_models import generate_uuid
            
            # 生成新的存档ID
            new_save_id = generate_uuid()
            save_path = os.path.join(self.save_directory, new_save_id)
            
            # 解压存档
            with zipfile.ZipFile(import_path, 'r') as zipf:
                zipf.extractall(save_path)
            
            # 读取元数据
            metadata_path = os.path.join(save_path, "metadata.json")
            if not os.path.exists(metadata_path):
                shutil.rmtree(save_path)
                return False, "无效的存档文件"
            
            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata_dict = json.load(f)
            
            # 更新存档ID
            metadata_dict['save_id'] = new_save_id
            metadata_dict['save_name'] += " (导入)"
            metadata = SaveMetadata.from_dict(metadata_dict)
            
            # 保存到索引数据库
            self._save_metadata(metadata)
            
            # 更新元数据文件
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata.to_dict(), f, ensure_ascii=False, indent=2)
            
            return True, f"存档导入成功: {metadata.save_name}"
            
        except Exception as e:
            return False, f"导入存档失败: {e}"
    
    def create_auto_save(self) -> Tuple[bool, str]:
        """创建自动存档"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
        return self.create_save(f"自动存档 {timestamp}", SaveType.AUTO, "系统自动创建的存档")
    
    def create_snapshot(self, description: str = "") -> Tuple[bool, str]:
        """创建快照"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
        return self.create_save(f"快照 {timestamp}", SaveType.SNAPSHOT, description)
    
    def _save_metadata(self, metadata: SaveMetadata):
        """保存元数据到索引数据库"""
        conn = sqlite3.connect(self.index_db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO save_metadata 
            (save_id, save_name, save_type, created_at, world_name, current_day, 
             current_season, npc_count, total_cash, player_mode, description, file_size, version)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            metadata.save_id, metadata.save_name, metadata.save_type.value,
            metadata.created_at, metadata.world_name, metadata.current_day,
            metadata.current_season, metadata.npc_count, metadata.total_cash,
            metadata.player_mode, metadata.description, metadata.file_size, metadata.version
        ))
        
        conn.commit()
        conn.close()
    
    def _cleanup_old_saves(self, save_type: SaveType, max_count: int):
        """清理旧存档"""
        saves = self.get_save_list(save_type)
        if len(saves) > max_count:
            # 删除最旧的存档
            for save in saves[max_count:]:
                self.delete_save(save.save_id)
    
    def _calculate_directory_size(self, directory: str) -> int:
        """计算目录大小"""
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                total_size += os.path.getsize(filepath)
        return total_size
    
    def get_save_statistics(self) -> Dict[str, Any]:
        """获取存档统计信息"""
        saves = self.get_save_list()
        
        stats = {
            "total_saves": len(saves),
            "manual_saves": len([s for s in saves if s.save_type == SaveType.MANUAL]),
            "auto_saves": len([s for s in saves if s.save_type == SaveType.AUTO]),
            "snapshots": len([s for s in saves if s.save_type == SaveType.SNAPSHOT]),
            "total_size": sum(s.file_size for s in saves),
            "oldest_save": min(saves, key=lambda x: x.created_at).created_at if saves else None,
            "newest_save": max(saves, key=lambda x: x.created_at).created_at if saves else None
        }
        
        return stats
