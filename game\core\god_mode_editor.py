# -*- coding: utf-8 -*-
"""
上帝模式编辑器
提供世界编辑、实时数据修改、调试工具等功能
"""

import json
import copy
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from .data_models import NPC, World, Region, Block, generate_uuid
from .database import DatabaseManager
from .economy_system import EconomySystem
from .market_events import MarketEventSystem
from .time_system import TimeSystem


class EditOperation(Enum):
    """编辑操作类型"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    BATCH_UPDATE = "batch_update"


class EntityType(Enum):
    """实体类型"""
    NPC = "npc"
    WORLD = "world"
    REGION = "region"
    BLOCK = "block"
    ECONOMY = "economy"
    MARKET_EVENT = "market_event"
    TIME = "time"


@dataclass
class EditCommand:
    """编辑命令"""
    id: str
    operation: EditOperation
    entity_type: EntityType
    entity_id: str
    old_data: Dict[str, Any] = field(default_factory=dict)
    new_data: Dict[str, Any] = field(default_factory=dict)
    timestamp: str = ""
    description: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)


class GodModeEditor:
    """上帝模式编辑器"""
    
    def __init__(self, db_manager: DatabaseManager, time_system: TimeSystem):
        self.db = db_manager
        self.time_system = time_system
        self.edit_history: List[EditCommand] = []
        self.undo_stack: List[EditCommand] = []
        self.redo_stack: List[EditCommand] = []
        
        # 编辑器设置
        self.auto_validate = True
        self.auto_backup = True
        self.max_history_size = 100
        
        # 数据缓存
        self._cached_data: Dict[str, Any] = {}
        self._cache_timestamp: Dict[str, str] = {}
    
    def edit_npc(self, npc_id: str, changes: Dict[str, Any]) -> Tuple[bool, str]:
        """编辑NPC属性"""
        try:
            # 加载NPC
            npc = self.db.load_npc(npc_id)
            if not npc:
                return False, f"NPC {npc_id} 不存在"
            
            # 备份原始数据
            old_data = self._serialize_npc(npc)
            
            # 验证更改
            validation = self._validate_npc_changes(npc, changes)
            if not validation.is_valid:
                return False, f"验证失败: {'; '.join(validation.errors)}"
            
            # 应用更改
            success, message = self._apply_npc_changes(npc, changes)
            if not success:
                return False, message
            
            # 保存到数据库
            self.db.save_npc(npc)
            
            # 记录编辑命令
            command = EditCommand(
                id=generate_uuid(),
                operation=EditOperation.UPDATE,
                entity_type=EntityType.NPC,
                entity_id=npc_id,
                old_data=old_data,
                new_data=changes,
                description=f"编辑NPC {npc.name}"
            )
            self._record_command(command)
            
            return True, f"成功编辑NPC {npc.name}"
            
        except Exception as e:
            return False, f"编辑失败: {e}"
    
    def batch_edit_npcs(self, npc_ids: List[str], changes: Dict[str, Any]) -> Tuple[int, List[str]]:
        """批量编辑NPC"""
        success_count = 0
        errors = []
        
        for npc_id in npc_ids:
            success, message = self.edit_npc(npc_id, changes)
            if success:
                success_count += 1
            else:
                errors.append(f"{npc_id}: {message}")
        
        return success_count, errors
    
    def create_npc(self, npc_data: Dict[str, Any]) -> Tuple[bool, str]:
        """创建新NPC"""
        try:
            from .npc_generator import NPCGenerator
            npc_generator = NPCGenerator(self.db)
            
            # 生成基础NPC
            npc = npc_generator.generate_npc()
            
            # 应用自定义数据
            success, message = self._apply_npc_changes(npc, npc_data)
            if not success:
                return False, message
            
            # 保存到数据库
            self.db.save_npc(npc)
            
            # 记录命令
            command = EditCommand(
                id=generate_uuid(),
                operation=EditOperation.CREATE,
                entity_type=EntityType.NPC,
                entity_id=npc.id,
                new_data=npc_data,
                description=f"创建NPC {npc.name}"
            )
            self._record_command(command)
            
            return True, f"成功创建NPC {npc.name} (ID: {npc.id})"
            
        except Exception as e:
            return False, f"创建失败: {e}"
    
    def delete_npc(self, npc_id: str) -> Tuple[bool, str]:
        """删除NPC"""
        try:
            npc = self.db.load_npc(npc_id)
            if not npc:
                return False, f"NPC {npc_id} 不存在"
            
            # 备份数据
            old_data = self._serialize_npc(npc)
            
            # 删除NPC
            success = self.db.delete_npc(npc_id)
            if not success:
                return False, "数据库删除失败"
            
            # 记录命令
            command = EditCommand(
                id=generate_uuid(),
                operation=EditOperation.DELETE,
                entity_type=EntityType.NPC,
                entity_id=npc_id,
                old_data=old_data,
                description=f"删除NPC {npc.name}"
            )
            self._record_command(command)
            
            return True, f"成功删除NPC {npc.name}"
            
        except Exception as e:
            return False, f"删除失败: {e}"
    
    def edit_economy(self, changes: Dict[str, Any]) -> Tuple[bool, str]:
        """编辑经济系统"""
        try:
            economy_system = self.time_system.economy_system
            
            # 备份当前状态
            old_data = {
                "current_season": economy_system.current_season,
                "daily_transaction_count": economy_system.daily_transaction_count
            }
            
            # 应用更改
            if "season" in changes:
                economy_system.update_seasonal_factors(changes["season"])
            
            if "market_fluctuation" in changes:
                economy_system.simulate_market_fluctuations()
            
            if "reset_transactions" in changes and changes["reset_transactions"]:
                economy_system.daily_transaction_count = 0
            
            # 记录命令
            command = EditCommand(
                id=generate_uuid(),
                operation=EditOperation.UPDATE,
                entity_type=EntityType.ECONOMY,
                entity_id="economy_system",
                old_data=old_data,
                new_data=changes,
                description="编辑经济系统"
            )
            self._record_command(command)
            
            return True, "经济系统编辑成功"
            
        except Exception as e:
            return False, f"经济系统编辑失败: {e}"
    
    def edit_time(self, changes: Dict[str, Any]) -> Tuple[bool, str]:
        """编辑时间系统"""
        try:
            # 备份当前时间状态
            old_data = {
                "current_day": self.time_system.current_day,
                "current_hour": self.time_system.current_hour,
                "current_season": self.time_system.current_season,
                "current_year": self.time_system.current_year
            }
            
            # 应用时间更改
            if "day" in changes:
                self.time_system.current_day = max(1, changes["day"])
            
            if "hour" in changes:
                self.time_system.current_hour = max(0, min(23, changes["hour"]))
            
            if "season" in changes:
                if changes["season"] in self.time_system.season_order:
                    self.time_system.current_season = changes["season"]
                    self.time_system.economy_system.update_seasonal_factors(changes["season"])
            
            if "year" in changes:
                self.time_system.current_year = max(1, changes["year"])
            
            # 记录命令
            command = EditCommand(
                id=generate_uuid(),
                operation=EditOperation.UPDATE,
                entity_type=EntityType.TIME,
                entity_id="time_system",
                old_data=old_data,
                new_data=changes,
                description="编辑时间系统"
            )
            self._record_command(command)
            
            return True, "时间系统编辑成功"
            
        except Exception as e:
            return False, f"时间系统编辑失败: {e}"
    
    def create_market_event(self, event_data: Dict[str, Any]) -> Tuple[bool, str]:
        """创建市场事件"""
        try:
            from .market_events import MarketEvent, EventType, EventSeverity
            
            # 创建市场事件
            event = MarketEvent(
                id=generate_uuid(),
                name=event_data.get("name", "自定义事件"),
                description=event_data.get("description", ""),
                event_type=EventType(event_data.get("type", "economic_boom")),
                severity=EventSeverity(event_data.get("severity", "moderate")),
                affected_categories=event_data.get("affected_categories", ["all"]),
                price_multiplier=event_data.get("price_multiplier", 1.0),
                supply_multiplier=event_data.get("supply_multiplier", 1.0),
                demand_multiplier=event_data.get("demand_multiplier", 1.0),
                duration_days=event_data.get("duration_days", 7),
                created_at=datetime.now().isoformat()
            )
            
            # 激活事件
            self.time_system.market_event_system.activate_event(event)
            
            # 记录命令
            command = EditCommand(
                id=generate_uuid(),
                operation=EditOperation.CREATE,
                entity_type=EntityType.MARKET_EVENT,
                entity_id=event.id,
                new_data=event_data,
                description=f"创建市场事件 {event.name}"
            )
            self._record_command(command)
            
            return True, f"成功创建市场事件 {event.name}"
            
        except Exception as e:
            return False, f"创建市场事件失败: {e}"
    
    def undo(self) -> Tuple[bool, str]:
        """撤销上一个操作"""
        if not self.undo_stack:
            return False, "没有可撤销的操作"
        
        try:
            command = self.undo_stack.pop()
            
            # 执行撤销操作
            success = self._execute_undo(command)
            if success:
                self.redo_stack.append(command)
                return True, f"已撤销: {command.description}"
            else:
                return False, "撤销操作失败"
                
        except Exception as e:
            return False, f"撤销失败: {e}"
    
    def redo(self) -> Tuple[bool, str]:
        """重做上一个撤销的操作"""
        if not self.redo_stack:
            return False, "没有可重做的操作"
        
        try:
            command = self.redo_stack.pop()
            
            # 重新执行操作
            success = self._execute_redo(command)
            if success:
                self.undo_stack.append(command)
                return True, f"已重做: {command.description}"
            else:
                return False, "重做操作失败"
                
        except Exception as e:
            return False, f"重做失败: {e}"
    
    def get_edit_history(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取编辑历史"""
        history = []
        for command in self.edit_history[-limit:]:
            history.append({
                "id": command.id,
                "operation": command.operation.value,
                "entity_type": command.entity_type.value,
                "entity_id": command.entity_id,
                "description": command.description,
                "timestamp": command.timestamp
            })
        return history
    
    def _serialize_npc(self, npc: NPC) -> Dict[str, Any]:
        """序列化NPC数据"""
        return {
            "name": npc.name,
            "age": npc.age,
            "gender": npc.gender.value,
            "education_level": npc.education_level.value,
            "cash": npc.cash,
            "bank_balance": npc.bank_balance,
            "monthly_income": npc.monthly_income,
            "current_location": npc.current_location,
            "daily_stats": {
                "energy": npc.daily_stats.energy,
                "happiness": npc.daily_stats.happiness,
                "stress": npc.daily_stats.stress,
                "health": npc.daily_stats.health,
                "hunger": npc.daily_stats.hunger
            }
        }
    
    def _validate_npc_changes(self, npc: NPC, changes: Dict[str, Any]) -> ValidationResult:
        """验证NPC更改"""
        result = ValidationResult(is_valid=True)
        
        # 验证年龄
        if "age" in changes:
            age = changes["age"]
            if not isinstance(age, int) or age < 0 or age > 150:
                result.errors.append("年龄必须是0-150之间的整数")
                result.is_valid = False
        
        # 验证金钱
        if "cash" in changes:
            cash = changes["cash"]
            if not isinstance(cash, (int, float)) or cash < 0:
                result.errors.append("现金必须是非负数")
                result.is_valid = False
        
        if "bank_balance" in changes:
            balance = changes["bank_balance"]
            if not isinstance(balance, (int, float)) or balance < 0:
                result.errors.append("银行余额必须是非负数")
                result.is_valid = False
        
        # 验证日常状态
        if "daily_stats" in changes:
            stats = changes["daily_stats"]
            for stat_name, value in stats.items():
                if not isinstance(value, (int, float)) or value < 0 or value > 100:
                    result.errors.append(f"{stat_name}必须是0-100之间的数值")
                    result.is_valid = False
        
        return result
    
    def _apply_npc_changes(self, npc: NPC, changes: Dict[str, Any]) -> Tuple[bool, str]:
        """应用NPC更改"""
        try:
            if "name" in changes:
                npc.name = str(changes["name"])
            
            if "age" in changes:
                npc.age = int(changes["age"])
            
            if "cash" in changes:
                npc.cash = float(changes["cash"])
            
            if "bank_balance" in changes:
                npc.bank_balance = float(changes["bank_balance"])
            
            if "monthly_income" in changes:
                npc.monthly_income = float(changes["monthly_income"])
            
            if "current_location" in changes:
                npc.current_location = str(changes["current_location"])
            
            if "daily_stats" in changes:
                stats = changes["daily_stats"]
                if "energy" in stats:
                    npc.daily_stats.energy = float(stats["energy"])
                if "happiness" in stats:
                    npc.daily_stats.happiness = float(stats["happiness"])
                if "stress" in stats:
                    npc.daily_stats.stress = float(stats["stress"])
                if "health" in stats:
                    npc.daily_stats.health = float(stats["health"])
                if "hunger" in stats:
                    npc.daily_stats.hunger = float(stats["hunger"])
            
            return True, "更改应用成功"
            
        except Exception as e:
            return False, f"应用更改失败: {e}"
    
    def _record_command(self, command: EditCommand):
        """记录编辑命令"""
        self.edit_history.append(command)
        self.undo_stack.append(command)
        self.redo_stack.clear()  # 清空重做栈
        
        # 限制历史记录大小
        if len(self.edit_history) > self.max_history_size:
            self.edit_history = self.edit_history[-self.max_history_size:]
        
        if len(self.undo_stack) > self.max_history_size:
            self.undo_stack = self.undo_stack[-self.max_history_size:]
    
    def _execute_undo(self, command: EditCommand) -> bool:
        """执行撤销操作"""
        try:
            if command.entity_type == EntityType.NPC:
                if command.operation == EditOperation.UPDATE:
                    npc = self.db.load_npc(command.entity_id)
                    if npc:
                        self._apply_npc_changes(npc, command.old_data)
                        self.db.save_npc(npc)
                        return True
                elif command.operation == EditOperation.CREATE:
                    return self.db.delete_npc(command.entity_id)
                elif command.operation == EditOperation.DELETE:
                    # 重新创建被删除的NPC（简化处理）
                    return True
            
            return False
            
        except Exception as e:
            print(f"撤销操作失败: {e}")
            return False
    
    def _execute_redo(self, command: EditCommand) -> bool:
        """执行重做操作"""
        try:
            if command.entity_type == EntityType.NPC:
                if command.operation == EditOperation.UPDATE:
                    npc = self.db.load_npc(command.entity_id)
                    if npc:
                        self._apply_npc_changes(npc, command.new_data)
                        self.db.save_npc(npc)
                        return True
                elif command.operation == EditOperation.CREATE:
                    # 重新创建NPC（简化处理）
                    return True
                elif command.operation == EditOperation.DELETE:
                    return self.db.delete_npc(command.entity_id)
            
            return False
            
        except Exception as e:
            print(f"重做操作失败: {e}")
            return False
