#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试M1阶段系统功能
包括NPC系统、时间系统、行为系统和经济系统
"""

import sys
import os

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

from core import (
    DatabaseManager, WorldGenerator, NPCGenerator, 
    TimeSystem, BehaviorSystem, EconomySystem
)

def test_m1_system():
    """测试M1阶段系统功能"""
    print("开始测试M1阶段系统...")
    
    # 1. 初始化系统
    print("\n1. 初始化系统...")
    db_manager = DatabaseManager("test_m1_world.db")
    world_generator = WorldGenerator(db_manager)
    npc_generator = NPCGenerator(db_manager)
    time_system = TimeSystem(db_manager)
    behavior_system = BehaviorSystem(db_manager)
    economy_system = EconomySystem(db_manager)
    print("✓ 所有系统初始化成功")
    
    # 2. 生成测试世界
    print("\n2. 生成测试世界...")
    world = world_generator.generate_world(
        world_name="M1测试世界",
        num_countries=1,
        provinces_per_country=(1, 1),
        cities_per_province=(1, 1),
        districts_per_city=(2, 2),
        villages_per_city=(1, 1)
    )
    print(f"✓ 世界生成成功: {world.name}")
    
    # 3. 生成测试NPC
    print("\n3. 生成测试NPC...")
    test_npcs = []
    for i in range(10):
        npc = npc_generator.generate_npc(
            age_range=(20, 50),
            residence_building_id=f"building_{i+1}"
        )
        db_manager.save_npc(npc)
        test_npcs.append(npc)
    
    print(f"✓ 生成了 {len(test_npcs)} 个NPC:")
    for npc in test_npcs[:5]:  # 只显示前5个
        job_info = f"({npc.current_job.title})" if npc.current_job else "(无业)"
        print(f"  - {npc.name}, {npc.age}岁, {npc.gender.value} {job_info}")
    
    # 4. 测试时间系统
    print("\n4. 测试时间系统...")
    time_info = time_system.get_current_time_info()
    print(f"✓ 当前时间: {time_info['time_string']} ({time_info['period']})")
    
    # 生成日程安排
    for npc in test_npcs[:3]:  # 为前3个NPC生成日程
        schedule = time_system.generate_daily_schedule(npc)
        print(f"  - {npc.name} 的日程安排:")
        for hour in range(6, 23, 4):  # 每4小时显示一次
            activity = schedule.get_activity(hour)
            if activity:
                print(f"    {hour:02d}:00 - {activity.name}")
    
    # 5. 测试行为系统
    print("\n5. 测试行为系统...")
    for npc in test_npcs[:3]:
        intentions = behavior_system.generate_intentions(npc, time_system.current_hour)
        print(f"  - {npc.name} 的意图 ({len(intentions)}个):")
        for intention in intentions[:3]:  # 显示前3个意图
            print(f"    {intention.description} (优先级: {intention.priority:.1f}, 紧急度: {intention.urgency:.1f})")
        
        # 创建行动计划
        plan = behavior_system.create_action_plan(npc, intentions, time_system.current_hour)
        if plan.scheduled_activities:
            print(f"    已安排 {len(plan.scheduled_activities)} 个活动")
        if plan.conflicts:
            print(f"    冲突: {len(plan.conflicts)} 个")
    
    # 6. 测试经济系统
    print("\n6. 测试经济系统...")
    
    # 获取经济摘要
    economy_summary = economy_system.get_economic_summary()
    print("✓ 经济系统摘要:")
    print(f"  - NPC总数: {economy_summary['total_npcs']}")
    print(f"  - 总现金: ¥{economy_summary['total_cash']:,.0f}")
    print(f"  - 月总收入: ¥{economy_summary['total_monthly_income']:,.0f}")
    print(f"  - 月总支出: ¥{economy_summary['total_monthly_expenses']:,.0f}")
    print(f"  - 就业统计: {economy_summary['employment_stats']}")
    
    # 测试消费行为
    print("\n  测试消费行为:")
    for npc in test_npcs[:3]:
        if npc.monthly_income > 0:
            transactions = economy_system.process_consumption(npc)
            if transactions:
                total_spent = sum(t.total_amount for t in transactions)
                print(f"    {npc.name} 消费了 ¥{total_spent:.0f} ({len(transactions)}笔交易)")
            else:
                print(f"    {npc.name} 没有进行消费")
    
    # 7. 测试时间推进
    print("\n7. 测试时间推进...")
    print("推进3小时...")
    
    for hour in range(3):
        print(f"\n  === 第{hour+1}小时 ===")
        
        # 推进时间
        results = time_system.advance_time(1)
        
        # 显示活动结果
        if results["completed_activities"]:
            print("  完成的活动:")
            for activity in results["completed_activities"][:5]:  # 只显示前5个
                print(f"    {activity}")
        
        # 显示状态变化
        if results["status_changes"]:
            print("  状态变化:")
            for change in results["status_changes"][:3]:  # 只显示前3个
                print(f"    {change}")
        
        # 更新行为计划
        for npc in test_npcs[:2]:  # 只处理前2个NPC
            intentions = behavior_system.generate_intentions(npc, time_system.current_hour)
            plan = behavior_system.create_action_plan(npc, intentions, time_system.current_hour)
            behavior_system.update_plan(npc.id, plan)
        
        # 显示当前时间
        time_info = time_system.get_current_time_info()
        print(f"  当前时间: {time_info['time_string']}")
    
    # 8. 最终状态检查
    print("\n8. 最终状态检查...")
    
    # 重新加载NPC查看状态变化
    updated_npcs = []
    for npc in test_npcs:
        updated_npc = db_manager.load_npc(npc.id)
        if updated_npc:
            updated_npcs.append(updated_npc)
    
    print("✓ NPC状态变化:")
    for i, (original, updated) in enumerate(zip(test_npcs[:3], updated_npcs[:3])):
        print(f"  {updated.name}:")
        print(f"    精力: {original.daily_stats.energy:.1f} → {updated.daily_stats.energy:.1f}")
        print(f"    幸福: {original.daily_stats.happiness:.1f} → {updated.daily_stats.happiness:.1f}")
        print(f"    压力: {original.daily_stats.stress:.1f} → {updated.daily_stats.stress:.1f}")
        
        if updated.current_activity:
            print(f"    当前活动: {updated.current_activity.name}")
    
    # 获取活跃NPC摘要
    npc_summary = time_system.get_active_npcs_summary()
    print(f"\n✓ 活跃NPC摘要 ({len(npc_summary)}个):")
    for summary in npc_summary[:5]:
        print(f"  {summary['name']}: {summary['current_activity']} (精力:{summary['energy']:.0f})")
    
    print("\n✅ M1阶段系统测试完成！")
    print("\n🎯 测试结果:")
    print("  ✓ NPC数据模型 - 完整的属性系统")
    print("  ✓ 时间系统 - 24小时回合制推进")
    print("  ✓ 行为系统 - 意图生成和行动计划")
    print("  ✓ 经济系统 - 工作机制和消费行为")
    print("  ✓ 数据持久化 - 所有数据正确保存和加载")
    
    return True

if __name__ == "__main__":
    try:
        success = test_m1_system()
        if success:
            print("\n🎉 M1阶段系统测试成功！可以开始开发UI界面了。")
        else:
            print("\n❌ M1阶段系统测试失败，请检查错误。")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
