[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:M0: 地图生成与行政区数据浏览 DESCRIPTION:实现基础的世界地图生成系统，包括多级行政区划（世界>国家>省份>市>区>村）的数据结构和基础浏览功能
--[x] NAME:设计数据结构 DESCRIPTION:设计World、Region、Block等核心数据类的结构，包括层级编码系统和基础属性
--[x] NAME:实现地图生成器 DESCRIPTION:创建世界地图生成算法，能够生成多级行政区划的层次结构
--[x] NAME:创建数据存储层 DESCRIPTION:实现SQLite数据库设计和基础的CRUD操作，支持地图数据的持久化
--[x] NAME:开发基础UI界面 DESCRIPTION:在Ren'Py中创建地图浏览界面，支持层级导航和信息展示
-[x] NAME:M1: 单NPC回合制日常系统 DESCRIPTION:实现单个NPC的基础属性系统、日常行为循环和职业/消费闭环
--[x] NAME:设计NPC数据模型 DESCRIPTION:创建完整的NPC数据结构，包括基础属性、经济属性、心理属性、技能系统和日常状态
--[x] NAME:实现时间系统 DESCRIPTION:创建24小时回合制时间推进系统，包括精力值管理和日常活动调度
--[x] NAME:开发行为系统 DESCRIPTION:实现NPC意图生成、行为调度器和冲突解决机制
--[x] NAME:创建职业与消费系统 DESCRIPTION:实现工作机制、消费行为和基础经济闭环
--[x] NAME:开发NPC管理界面 DESCRIPTION:创建NPC信息展示、状态监控和交互界面
-[ ] NAME:M2: 多NPC经济结算系统 DESCRIPTION:实现多NPC之间的经济交互、动态价格系统和市场机制
-[ ] NAME:M3: AI事件流与新闻系统 DESCRIPTION:集成AI生成事件流、新闻面板和故事叙述系统
-[ ] NAME:M4: 角色切换与上帝模式 DESCRIPTION:实现玩家角色切换功能和沙盒编辑的上帝模式
-[ ] NAME:M5: AI演出系统 DESCRIPTION:集成TTS、文生图等AI演出功能，提升视听沉浸感