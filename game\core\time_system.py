# -*- coding: utf-8 -*-
"""
时间系统
实现24小时回合制时间推进，包括精力值管理和日常活动调度
"""

import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from .data_models import NPC, Activity, ActivityType, generate_uuid
from .database import DatabaseManager


@dataclass
class TimeSlot:
    """时间段"""
    hour: int  # 0-23
    minute: int = 0
    duration: int = 60  # 持续时间（分钟）
    
    def __str__(self):
        return f"{self.hour:02d}:{self.minute:02d}"
    
    def to_minutes(self) -> int:
        """转换为分钟数（从0点开始）"""
        return self.hour * 60 + self.minute


@dataclass
class DaySchedule:
    """日程安排"""
    npc_id: str
    day: int
    activities: Dict[int, Activity] = field(default_factory=dict)  # hour -> Activity
    
    def add_activity(self, hour: int, activity: Activity):
        """添加活动"""
        self.activities[hour] = activity
    
    def get_activity(self, hour: int) -> Optional[Activity]:
        """获取指定时间的活动"""
        return self.activities.get(hour)
    
    def is_free(self, hour: int, duration: int = 1) -> bool:
        """检查指定时间段是否空闲"""
        for h in range(hour, hour + duration):
            if h in self.activities:
                return False
        return True


class TimeSystem:
    """时间系统管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.current_day = 1
        self.current_hour = 6  # 从早上6点开始
        self.schedules: Dict[str, DaySchedule] = {}  # npc_id -> DaySchedule
        
        # 默认活动模板
        self.default_activities = {
            "sleep": Activity(
                id="sleep",
                type=ActivityType.SLEEP,
                name="睡觉",
                duration=480,  # 8小时
                energy_cost=-80.0,  # 恢复精力
                happiness_gain=10.0,
                stress_change=-20.0
            ),
            "work": Activity(
                id="work",
                type=ActivityType.WORK,
                name="工作",
                duration=480,  # 8小时
                energy_cost=40.0,
                happiness_gain=-5.0,
                stress_change=15.0
            ),
            "eat_breakfast": Activity(
                id="eat_breakfast",
                type=ActivityType.EAT,
                name="吃早餐",
                duration=30,
                energy_cost=-10.0,
                happiness_gain=5.0,
                stress_change=-5.0
            ),
            "eat_lunch": Activity(
                id="eat_lunch",
                type=ActivityType.EAT,
                name="吃午餐",
                duration=60,
                energy_cost=-15.0,
                happiness_gain=8.0,
                stress_change=-8.0
            ),
            "eat_dinner": Activity(
                id="eat_dinner",
                type=ActivityType.EAT,
                name="吃晚餐",
                duration=90,
                energy_cost=-20.0,
                happiness_gain=10.0,
                stress_change=-10.0
            ),
            "commute": Activity(
                id="commute",
                type=ActivityType.COMMUTE,
                name="通勤",
                duration=60,
                energy_cost=10.0,
                happiness_gain=-5.0,
                stress_change=5.0
            ),
            "rest": Activity(
                id="rest",
                type=ActivityType.REST,
                name="休息",
                duration=120,
                energy_cost=-20.0,
                happiness_gain=15.0,
                stress_change=-15.0
            ),
            "entertainment": Activity(
                id="entertainment",
                type=ActivityType.ENTERTAINMENT,
                name="娱乐",
                duration=120,
                energy_cost=5.0,
                happiness_gain=25.0,
                stress_change=-20.0
            )
        }
    
    def advance_time(self, hours: int = 1) -> Dict[str, List[str]]:
        """推进时间"""
        results = {"completed_activities": [], "status_changes": []}
        
        for _ in range(hours):
            # 处理当前小时的所有NPC活动
            active_npcs = self.db.load_all_active_npcs()
            
            for npc in active_npcs:
                # 获取或创建日程
                if npc.id not in self.schedules:
                    self.schedules[npc.id] = self.generate_daily_schedule(npc)
                
                schedule = self.schedules[npc.id]
                activity = schedule.get_activity(self.current_hour)
                
                if activity:
                    # 执行活动
                    self.execute_activity(npc, activity)
                    results["completed_activities"].append(f"{npc.name}: {activity.name}")
                
                # 更新NPC状态
                status_change = self.update_npc_status(npc)
                if status_change:
                    results["status_changes"].append(status_change)
                
                # 保存NPC状态
                self.db.save_npc(npc)
            
            # 推进到下一小时
            self.current_hour += 1
            if self.current_hour >= 24:
                self.current_hour = 0
                self.current_day += 1
                self.schedules.clear()  # 清空日程，重新生成
        
        return results
    
    def generate_daily_schedule(self, npc: NPC) -> DaySchedule:
        """为NPC生成日程安排"""
        schedule = DaySchedule(npc.id, self.current_day)
        
        # 基础作息时间
        sleep_start = 22  # 晚上10点睡觉
        sleep_end = 6     # 早上6点起床
        
        # 睡觉时间
        for hour in range(sleep_start, 24):
            schedule.add_activity(hour, self.default_activities["sleep"])
        for hour in range(0, sleep_end):
            schedule.add_activity(hour, self.default_activities["sleep"])
        
        # 用餐时间
        schedule.add_activity(7, self.default_activities["eat_breakfast"])
        schedule.add_activity(12, self.default_activities["eat_lunch"])
        schedule.add_activity(18, self.default_activities["eat_dinner"])
        
        # 工作时间（如果有工作）
        if npc.current_job and npc.current_job.is_active:
            work_start = 9
            work_hours = npc.current_job.work_hours_per_day
            
            # 通勤
            schedule.add_activity(work_start - 1, self.default_activities["commute"])
            schedule.add_activity(work_start + work_hours, self.default_activities["commute"])
            
            # 工作
            for hour in range(work_start, work_start + work_hours):
                if schedule.is_free(hour):
                    schedule.add_activity(hour, self.default_activities["work"])
        
        # 填充空闲时间
        for hour in range(6, 22):
            if schedule.is_free(hour):
                # 根据NPC性格选择活动
                if npc.personality.extraversion > 60 and hour >= 19:
                    schedule.add_activity(hour, self.default_activities["entertainment"])
                else:
                    schedule.add_activity(hour, self.default_activities["rest"])
        
        return schedule
    
    def execute_activity(self, npc: NPC, activity: Activity):
        """执行活动"""
        # 更新NPC状态
        npc.update_daily_stats(
            energy=-activity.energy_cost,
            happiness=activity.happiness_gain,
            stress=activity.stress_change
        )
        
        # 根据活动类型进行特殊处理
        if activity.type == ActivityType.WORK and npc.current_job:
            # 工作获得技能经验
            for skill_name in npc.current_job.required_skills:
                npc.add_skill_experience(skill_name, 1.0)
        
        elif activity.type == ActivityType.EAT:
            # 进食减少饥饿值
            npc.update_daily_stats(hunger=-30.0)
        
        elif activity.type == ActivityType.EXERCISE:
            # 运动提升健康
            npc.update_daily_stats(health=2.0)
        
        # 记录活动历史
        activity_record = Activity(
            id=generate_uuid(),
            type=activity.type,
            name=activity.name,
            duration=activity.duration,
            energy_cost=activity.energy_cost,
            happiness_gain=activity.happiness_gain,
            stress_change=activity.stress_change
        )
        
        npc.activity_history.append(activity_record)
        npc.current_activity = activity_record
        
        # 限制历史记录长度
        if len(npc.activity_history) > 100:
            npc.activity_history = npc.activity_history[-100:]
    
    def update_npc_status(self, npc: NPC) -> Optional[str]:
        """更新NPC状态"""
        status_changes = []
        
        # 自然状态变化
        npc.update_daily_stats(
            hunger=2.0,    # 饥饿值自然增长
            energy=-1.0,   # 精力自然消耗
            stress=0.5     # 压力自然积累
        )
        
        # 检查临界状态
        if npc.daily_stats.energy < 20:
            status_changes.append(f"{npc.name} 精力不足")
        
        if npc.daily_stats.hunger > 80:
            status_changes.append(f"{npc.name} 非常饥饿")
            npc.update_daily_stats(happiness=-10.0, health=-2.0)
        
        if npc.daily_stats.stress > 80:
            status_changes.append(f"{npc.name} 压力过大")
            npc.update_daily_stats(happiness=-15.0, health=-1.0)
        
        if npc.daily_stats.happiness < 20:
            status_changes.append(f"{npc.name} 情绪低落")
        
        return "; ".join(status_changes) if status_changes else None
    
    def get_current_time_info(self) -> Dict[str, any]:
        """获取当前时间信息"""
        return {
            "day": self.current_day,
            "hour": self.current_hour,
            "time_string": f"第{self.current_day}天 {self.current_hour:02d}:00",
            "period": self.get_time_period(self.current_hour)
        }
    
    def get_time_period(self, hour: int) -> str:
        """获取时间段描述"""
        if 6 <= hour < 12:
            return "上午"
        elif 12 <= hour < 18:
            return "下午"
        elif 18 <= hour < 22:
            return "晚上"
        else:
            return "深夜"
    
    def get_npc_schedule(self, npc_id: str) -> Optional[DaySchedule]:
        """获取NPC的日程安排"""
        return self.schedules.get(npc_id)
    
    def set_custom_activity(self, npc_id: str, hour: int, activity: Activity) -> bool:
        """为NPC设置自定义活动"""
        if npc_id not in self.schedules:
            npc = self.db.load_npc(npc_id)
            if not npc:
                return False
            self.schedules[npc_id] = self.generate_daily_schedule(npc)
        
        schedule = self.schedules[npc_id]
        if schedule.is_free(hour):
            schedule.add_activity(hour, activity)
            return True
        return False
    
    def get_active_npcs_summary(self) -> List[Dict[str, any]]:
        """获取所有活跃NPC的状态摘要"""
        active_npcs = self.db.load_all_active_npcs()
        summary = []
        
        for npc in active_npcs:
            current_activity = None
            if npc.id in self.schedules:
                schedule = self.schedules[npc.id]
                current_activity = schedule.get_activity(self.current_hour)
            
            summary.append({
                "id": npc.id,
                "name": npc.name,
                "age": npc.age,
                "current_activity": current_activity.name if current_activity else "空闲",
                "energy": npc.daily_stats.energy,
                "happiness": npc.daily_stats.happiness,
                "stress": npc.daily_stats.stress,
                "location": npc.current_location
            })
        
        return summary
