#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Ren'Py导入环境
模拟Ren'Py的导入过程
"""

import os
import sys
import traceback

def test_renpy_import():
    """测试Ren'Py风格的导入"""
    print("=== 测试Ren'Py导入环境 ===")
    
    # 模拟Ren'Py的Python路径设置
    game_path = os.path.join(os.getcwd(), 'game')
    print(f"添加game路径到sys.path: {game_path}")
    
    if game_path not in sys.path:
        sys.path.insert(0, game_path)
    
    # 模拟script.rpy中的导入过程
    try:
        print("尝试导入核心模块...")
        from core import DatabaseManager, WorldGenerator, World, Region, Block
        print("✓ 核心模块导入成功")
        
        # 测试初始化
        print("测试数据库管理器初始化...")
        db_manager = DatabaseManager()
        print("✓ 数据库管理器初始化成功")
        
        print("测试世界生成器初始化...")
        world_generator = WorldGenerator(db_manager)
        print("✓ 世界生成器初始化成功")
        
        # 测试变量设置
        print("设置全局变量...")
        current_world = None
        current_region = None
        current_blocks = []
        print("✓ 全局变量设置成功")
        
        # 测试数据库操作
        print("测试数据库操作...")
        existing_worlds = db_manager.list_worlds()
        print(f"✓ 找到 {len(existing_worlds)} 个现有世界")
        
        if existing_worlds:
            print("尝试加载第一个世界...")
            current_world = db_manager.load_world(existing_worlds[0]['id'])
            print("✓ 世界加载成功")
        else:
            print("ℹ 没有现有世界，current_world保持为None")
        
        print("\n🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 导入或初始化失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        return False

def test_variable_access():
    """测试变量访问"""
    print("\n=== 测试变量访问 ===")
    
    try:
        # 模拟Ren'Py中的变量检查
        game_path = os.path.join(os.getcwd(), 'game')
        if game_path not in sys.path:
            sys.path.insert(0, game_path)
        
        from core import DatabaseManager, WorldGenerator
        
        # 初始化变量
        db_manager = DatabaseManager()
        world_generator = WorldGenerator(db_manager)
        current_world = None
        
        # 测试变量访问（模拟script.rpy中的检查）
        print("测试 current_world 变量访问...")
        if current_world is None:
            print("✓ current_world is None - 正常")
        else:
            print(f"✓ current_world 已设置: {current_world}")
        
        print("✓ 变量访问测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 变量访问失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试流程"""
    print("🧪 Ren'Py导入环境测试")
    print("=" * 50)
    
    # 显示当前环境
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python版本: {sys.version}")
    print()
    
    # 测试导入
    import_ok = test_renpy_import()
    if not import_ok:
        print("\n❌ 导入测试失败")
        return
    
    # 测试变量访问
    var_ok = test_variable_access()
    if not var_ok:
        print("\n❌ 变量访问测试失败")
        return
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！")
    print("系统应该可以在Ren'Py中正常工作")
    
    print("\n如果Ren'Py仍然报错，可能的原因:")
    print("1. Ren'Py使用的Python版本与当前不同")
    print("2. Ren'Py的模块加载机制有特殊要求")
    print("3. 某些模块在Ren'Py环境中的行为不同")
    print("4. 缓存问题 - 尝试删除 game/core/__pycache__ 目录")

if __name__ == "__main__":
    main()
