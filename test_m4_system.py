#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
M4阶段系统测试
测试玩家角色切换和上帝模式系统
"""

import sys
import os
import random
from datetime import datetime

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

def test_m4_system():
    """测试M4阶段的玩家角色切换和上帝模式功能"""
    print("=" * 60)
    print("M4阶段系统测试：玩家角色切换 + 上帝模式")
    print("=" * 60)
    
    try:
        # 导入核心模块
        from core import DatabaseManager, WorldGenerator, NPCGenerator
        from core.time_system import TimeSystem
        from core.player_system import PlayerSystem, PlayerMode
        from core.god_mode_editor import GodModeEditor
        from core.save_system import SaveSystem, SaveType
        
        print("✓ 所有核心模块导入成功")
        
        # 1. 初始化系统
        print("\n1. 初始化系统...")
        db_manager = DatabaseManager("test_m4.db")
        world_generator = WorldGenerator(db_manager)
        npc_generator = NPCGenerator(db_manager)
        time_system = TimeSystem(db_manager)
        player_system = PlayerSystem(db_manager)
        god_mode_editor = GodModeEditor(db_manager, time_system)
        save_system = SaveSystem(db_manager, "test_saves")
        
        print("✓ 系统初始化完成")
        
        # 2. 创建测试环境
        print("\n2. 创建测试环境...")
        world = world_generator.generate_world("M4测试世界")
        
        test_npcs = []
        for i in range(10):
            npc = npc_generator.generate_npc()
            npc.monthly_income = random.randint(2000, 8000)
            npc.cash = random.randint(500, 2000)
            test_npcs.append(npc)
            db_manager.save_npc(npc)
        
        print(f"✓ 创建了世界和 {len(test_npcs)} 个NPC")
        
        # 3. 测试玩家系统
        print("\n3. 测试玩家系统...")
        
        # 测试初始状态
        initial_stats = player_system.get_player_stats()
        print(f"  初始模式: {initial_stats['current_mode']}")
        print(f"  可用角色: {initial_stats['available_characters']}")
        
        # 测试角色切换
        available_characters = player_system.get_available_characters()
        if available_characters:
            test_character = available_characters[0]
            print(f"  测试角色: {test_character.name}")
            
            # 切换到角色模式
            success = player_system.switch_to_character_mode(test_character.id)
            print(f"  切换到角色模式: {'成功' if success else '失败'}")
            
            if success:
                current_char = player_system.get_current_character()
                print(f"  当前控制角色: {current_char.name if current_char else 'None'}")
                
                # 测试角色权限
                can_work = player_system.can_perform_action("work")
                can_edit = player_system.can_perform_action("edit_world")
                print(f"  可以工作: {can_work}")
                print(f"  可以编辑世界: {can_edit}")
                
                # 记录一些操作
                player_system.record_action("work", {"description": "在办公室工作"})
                player_system.record_action("socialize", {"description": "与同事聊天", "memorable": True})
                
                # 获取角色体验
                experience = player_system.get_character_experience(test_character.id)
                if experience:
                    print(f"  角色体验记录: {len(experience.sessions)} 个会话")
                    print(f"  难忘事件: {len(experience.memorable_events)} 个")
        
        # 4. 测试上帝模式编辑器
        print("\n4. 测试上帝模式编辑器...")
        
        # 切换到上帝模式
        success = player_system.switch_to_god_mode()
        print(f"  切换到上帝模式: {'成功' if success else '失败'}")
        
        # 测试NPC编辑
        if test_npcs:
            target_npc = test_npcs[0]
            original_cash = target_npc.cash
            original_happiness = target_npc.daily_stats.happiness
            
            print(f"  编辑NPC: {target_npc.name}")
            print(f"    原始现金: ¥{original_cash:.0f}")
            print(f"    原始幸福度: {original_happiness:.1f}")
            
            # 编辑NPC属性
            changes = {
                "cash": original_cash + 1000,
                "daily_stats": {
                    "happiness": min(100, original_happiness + 20),
                    "energy": 80
                }
            }
            
            success, message = god_mode_editor.edit_npc(target_npc.id, changes)
            print(f"    编辑结果: {message}")
            
            if success:
                # 重新加载NPC验证更改
                updated_npc = db_manager.load_npc(target_npc.id)
                print(f"    新现金: ¥{updated_npc.cash:.0f}")
                print(f"    新幸福度: {updated_npc.daily_stats.happiness:.1f}")
        
        # 测试时间编辑
        print(f"  测试时间编辑...")
        original_day = time_system.current_day
        original_season = time_system.current_season
        
        print(f"    原始时间: 第{original_day}天, {original_season}")
        
        time_changes = {
            "day": original_day + 10,
            "season": "summer"
        }
        
        success, message = god_mode_editor.edit_time(time_changes)
        print(f"    时间编辑结果: {message}")
        
        if success:
            print(f"    新时间: 第{time_system.current_day}天, {time_system.current_season}")
        
        # 测试市场事件创建
        print(f"  测试市场事件创建...")
        event_data = {
            "name": "测试经济繁荣",
            "description": "这是一个测试用的经济繁荣事件",
            "type": "economic_boom",
            "severity": "moderate",
            "affected_categories": ["all"],
            "price_multiplier": 1.2,
            "supply_multiplier": 1.1,
            "demand_multiplier": 1.3,
            "duration_days": 5
        }
        
        success, message = god_mode_editor.create_market_event(event_data)
        print(f"    事件创建结果: {message}")
        
        if success:
            active_events = time_system.market_event_system.get_active_events()
            print(f"    当前活跃事件: {len(active_events)} 个")
        
        # 5. 测试撤销/重做功能
        print("\n5. 测试撤销/重做功能...")
        
        # 获取编辑历史
        history = god_mode_editor.get_edit_history(5)
        print(f"  编辑历史: {len(history)} 条记录")
        
        for i, record in enumerate(history):
            print(f"    {i+1}. {record['description']} ({record['operation']})")
        
        # 测试撤销
        if history:
            success, message = god_mode_editor.undo()
            print(f"  撤销操作: {message}")
            
            # 测试重做
            success, message = god_mode_editor.redo()
            print(f"  重做操作: {message}")
        
        # 6. 测试存档系统
        print("\n6. 测试存档系统...")
        
        # 创建手动存档
        success, message = save_system.create_save("M4测试存档", SaveType.MANUAL, "测试用存档")
        print(f"  创建存档: {message}")
        
        # 创建自动存档
        success, message = save_system.create_auto_save()
        print(f"  创建自动存档: {message}")
        
        # 创建快照
        success, message = save_system.create_snapshot("测试快照")
        print(f"  创建快照: {message}")
        
        # 获取存档列表
        saves = save_system.get_save_list()
        print(f"  存档列表: {len(saves)} 个存档")
        
        for save in saves[:3]:  # 显示前3个
            print(f"    - {save.save_name} ({save.save_type.value}) - {save.created_at[:19]}")
            print(f"      NPC数量: {save.npc_count}, 总现金: ¥{save.total_cash:.0f}")
        
        # 获取存档统计
        stats = save_system.get_save_statistics()
        print(f"  存档统计:")
        print(f"    总存档数: {stats['total_saves']}")
        print(f"    手动存档: {stats['manual_saves']}")
        print(f"    自动存档: {stats['auto_saves']}")
        print(f"    快照: {stats['snapshots']}")
        print(f"    总大小: {stats['total_size']} 字节")
        
        # 7. 测试视角系统
        print("\n7. 测试视角系统...")
        
        # 测试不同模式的视角信息
        modes_to_test = [
            (PlayerMode.GOD_MODE, "上帝模式"),
            (PlayerMode.OBSERVER_MODE, "观察者模式")
        ]
        
        for mode, mode_name in modes_to_test:
            if mode == PlayerMode.GOD_MODE:
                player_system.switch_to_god_mode()
            else:
                player_system.switch_to_observer_mode()
            
            perspective_info = player_system.get_perspective_info()
            print(f"  {mode_name}:")
            print(f"    视角: {perspective_info['perspective']}")
            print(f"    权限: 编辑世界={perspective_info['permissions']['can_edit_world']}")
            print(f"    权限: 控制NPC={perspective_info['permissions']['can_control_npcs']}")
        
        # 8. 测试自定义角色创建
        print("\n8. 测试自定义角色创建...")
        
        custom_character_data = {
            "name": "测试自定义角色",
            "age": 25,
            "gender": "male",
            "education_level": "university"
        }
        
        custom_char_id = player_system.create_custom_character(custom_character_data)
        if custom_char_id:
            print(f"  自定义角色创建成功: ID {custom_char_id}")
            
            # 切换到自定义角色
            success = player_system.switch_to_character_mode(custom_char_id)
            print(f"  切换到自定义角色: {'成功' if success else '失败'}")
        else:
            print("  自定义角色创建失败")
        
        # 9. 性能测试
        print("\n9. 性能测试...")
        
        import time
        
        # 测试批量NPC编辑性能
        start_time = time.time()
        
        batch_changes = {"cash": 5000, "daily_stats": {"energy": 90}}
        npc_ids = [npc.id for npc in test_npcs[:5]]
        
        success_count, errors = god_mode_editor.batch_edit_npcs(npc_ids, batch_changes)
        
        end_time = time.time()
        batch_time = end_time - start_time
        
        print(f"  批量编辑 {len(npc_ids)} 个NPC")
        print(f"  成功: {success_count}, 失败: {len(errors)}")
        print(f"  耗时: {batch_time:.3f} 秒")
        print(f"  平均每个NPC: {batch_time/len(npc_ids):.3f} 秒")
        
        # 10. 最终状态报告
        print("\n10. 最终系统状态...")
        
        final_player_stats = player_system.get_player_stats()
        final_time_info = time_system.get_current_time_info()
        final_economic_summary = time_system.economy_system.get_economic_summary()
        
        print("  玩家系统状态:")
        print(f"    当前模式: {final_player_stats['current_mode']}")
        print(f"    操作次数: {final_player_stats['actions_taken']}")
        print(f"    使用过的角色: {final_player_stats['characters_played']}")
        
        print("  世界状态:")
        print(f"    当前时间: {final_time_info['time_string']}")
        print(f"    经济活跃度: {final_time_info['economic_activity']}")
        print(f"    市场事件: {final_time_info['market_events']}")
        
        print("  经济状态:")
        print(f"    总现金: ¥{final_economic_summary['total_cash']:,.0f}")
        print(f"    市场活跃度: {final_economic_summary['market_activity']}")
        print(f"    今日交易: {final_economic_summary['daily_transactions']}")
        
        print("\n" + "=" * 60)
        print("✅ M4阶段系统测试完成！")
        print("✅ 玩家角色系统正常工作")
        print("✅ 上帝模式编辑器正常工作")
        print("✅ 角色切换功能正常工作")
        print("✅ 存档管理系统正常工作")
        print("✅ 撤销/重做功能正常工作")
        print("✅ 视角系统正常工作")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_m4_system()
    sys.exit(0 if success else 1)
