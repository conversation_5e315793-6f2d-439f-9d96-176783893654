#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Ren'Py环境中的核心系统
模拟Ren'Py环境，测试文件数据库管理器
"""

import sys
import os

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

def test_renpy_environment():
    """测试Ren'Py环境模拟"""
    print("=== 测试Ren'Py环境模拟 ===")
    
    # 模拟sqlite3不可用的情况
    original_modules = sys.modules.copy()
    if 'sqlite3' in sys.modules:
        del sys.modules['sqlite3']
    
    # 临时禁用sqlite3导入
    import builtins
    original_import = builtins.__import__
    
    def mock_import(name, *args, **kwargs):
        if name == 'sqlite3':
            raise ImportError("No module named 'sqlite3'")
        return original_import(name, *args, **kwargs)
    
    builtins.__import__ = mock_import
    
    try:
        # 测试核心模块导入
        print("1. 测试核心模块导入...")
        from core import DatabaseManager, WorldGenerator, World
        print("✓ 核心模块导入成功（使用文件数据库）")
        
        # 测试数据库管理器
        print("\n2. 测试文件数据库管理器...")
        db_manager = DatabaseManager("test_renpy_data")
        print("✓ 文件数据库管理器初始化成功")
        
        # 测试世界生成器
        print("\n3. 测试世界生成器...")
        world_generator = WorldGenerator(db_manager)
        print("✓ 世界生成器初始化成功")
        
        # 生成测试世界
        print("\n4. 生成测试世界...")
        world = world_generator.generate_world(
            world_name="Ren'Py测试世界",
            num_countries=1,
            provinces_per_country=(1, 1),
            cities_per_province=(1, 1),
            districts_per_city=(2, 2),
            villages_per_city=(1, 1)
        )
        print(f"✓ 世界生成成功: {world.name}")
        print(f"  - 总人口: {world.total_population:,}")
        print(f"  - 行政区域数: {world.total_regions}")
        print(f"  - 区块数: {world.total_blocks}")
        
        # 测试数据持久化
        print("\n5. 测试数据持久化...")
        saved = db_manager.save_world(world)
        if saved:
            print("✓ 世界数据保存成功")
        else:
            print("✗ 世界数据保存失败")
            return False
        
        # 测试数据加载
        print("\n6. 测试数据加载...")
        loaded_world = db_manager.load_world(world.id)
        if loaded_world and loaded_world.name == world.name:
            print(f"✓ 世界数据加载成功: {loaded_world.name}")
        else:
            print("✗ 世界数据加载失败")
            return False
        
        # 测试世界列表
        print("\n7. 测试世界列表...")
        worlds = db_manager.list_worlds()
        print(f"✓ 找到 {len(worlds)} 个世界:")
        for world_info in worlds:
            print(f"  - {world_info['name']} (ID: {world_info['id'][:8]}...)")
        
        # 测试区域查询
        print("\n8. 测试区域查询...")
        countries = db_manager.load_regions_by_parent(None)
        print(f"✓ 找到 {len(countries)} 个国家:")
        for country in countries:
            print(f"  - {country.name} (人口: {country.population:,})")
            
            provinces = db_manager.load_regions_by_parent(country.id)
            for province in provinces:
                print(f"    └─ {province.name} (人口: {province.population:,})")
                
                cities = db_manager.load_regions_by_parent(province.id)
                for city in cities:
                    print(f"      └─ {city.name} (人口: {city.population:,})")
                    
                    blocks = db_manager.load_blocks_by_parent(city.id)
                    for block in blocks:
                        print(f"        └─ {block.name} ({block.type.value}, 人口: {block.population:,})")
        
        print("\n✅ 所有Ren'Py环境测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 恢复原始导入函数
        builtins.__import__ = original_import
        
        # 清理测试数据
        import shutil
        if os.path.exists("test_renpy_data"):
            shutil.rmtree("test_renpy_data")

def test_normal_environment():
    """测试正常环境"""
    print("\n=== 测试正常环境 ===")
    
    try:
        from core import DatabaseManager, WorldGenerator
        print("✓ 正常环境下核心模块导入成功")
        
        db_manager = DatabaseManager("test_normal.db")
        print("✓ SQLite数据库管理器工作正常")
        
        # 清理测试文件
        if os.path.exists("test_normal.db"):
            os.remove("test_normal.db")
        
        return True
        
    except Exception as e:
        print(f"❌ 正常环境测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Ren'Py核心系统兼容性测试")
    print("=" * 50)
    
    # 测试正常环境
    normal_ok = test_normal_environment()
    
    # 测试Ren'Py环境模拟
    renpy_ok = test_renpy_environment()
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"  正常环境: {'✅ 通过' if normal_ok else '❌ 失败'}")
    print(f"  Ren'Py环境: {'✅ 通过' if renpy_ok else '❌ 失败'}")
    
    if normal_ok and renpy_ok:
        print("\n🎉 所有环境测试通过！")
        print("核心系统已完全兼容Ren'Py环境。")
        print("\n启动建议:")
        print("1. 运行 start_game.bat")
        print("2. 或使用Ren'Py启动器")
        print("3. 游戏现在应该可以正常加载核心系统")
        return True
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    main()
