@echo off
echo ========================================
echo           启动游戏脚本
echo ========================================
echo.

echo 正在查找Ren'Py...

REM 尝试常见的Ren'Py安装路径
set RENPY_FOUND=0

REM 检查当前目录
if exist "renpy.exe" (
    set RENPY_PATH=renpy.exe
    set RENPY_FOUND=1
    goto :found
)

REM 检查常见安装路径
if exist "C:\RenPy\renpy.exe" (
    set RENPY_PATH=C:\RenPy\renpy.exe
    set RENPY_FOUND=1
    goto :found
)

if exist "C:\Program Files\RenPy\renpy.exe" (
    set RENPY_PATH=C:\Program Files\RenPy\renpy.exe
    set RENPY_FOUND=1
    goto :found
)

if exist "C:\Program Files (x86)\RenPy\renpy.exe" (
    set RENPY_PATH=C:\Program Files (x86)\RenPy\renpy.exe
    set RENPY_FOUND=1
    goto :found
)

if exist "D:\RenPy\renpy.exe" (
    set RENPY_PATH=D:\RenPy\renpy.exe
    set RENPY_FOUND=1
    goto :found
)

REM 如果没有找到Ren'Py
if %RENPY_FOUND%==0 (
    echo ❌ 未找到Ren'Py可执行文件！
    echo.
    echo 请按照以下步骤安装Ren'Py：
    echo 1. 访问 https://www.renpy.org/latest.html
    echo 2. 下载Ren'Py SDK
    echo 3. 解压到任意目录（如 C:\RenPy\）
    echo 4. 重新运行此脚本
    echo.
    echo 或者手动编辑此脚本，在第6行设置正确的Ren'Py路径：
    echo set RENPY_PATH="您的Ren'Py路径\renpy.exe"
    echo.
    pause
    exit /b 1
)

:found
echo ✅ 找到Ren'Py: %RENPY_PATH%
echo.

echo 正在启动游戏...
echo 项目目录: %CD%
echo.

REM 启动游戏
"%RENPY_PATH%" .

REM 检查启动结果
if %ERRORLEVEL%==0 (
    echo.
    echo ✅ 游戏正常退出
) else (
    echo.
    echo ❌ 游戏启动失败，错误代码: %ERRORLEVEL%
    echo.
    echo 故障排除建议：
    echo 1. 确保项目文件完整
    echo 2. 检查Ren'Py版本（推荐8.0+）
    echo 3. 查看错误日志文件
    echo 4. 运行 python test_renpy_syntax.py 检查语法
)

echo.
pause
