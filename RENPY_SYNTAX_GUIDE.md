# Ren'Py 语法修复指南

## 常见语法错误及修复方法

### 1. textbutton 的 size 属性错误

**❌ 错误写法:**
```renpy
textbutton "按钮" action Return() size 16
```

**✅ 正确写法:**
```renpy
textbutton "按钮" action Return() text_size 16
```

**说明:** 在Ren'Py中，textbutton的文字大小属性是`text_size`，不是`size`。

### 2. 属性中的条件表达式错误

**❌ 错误写法:**
```renpy
frame:
    background "#34495e" if npc.is_active else "#2c3e50"
```

**✅ 正确写法:**
```renpy
python:
    bg_color = "#34495e" if npc.is_active else "#2c3e50"

frame:
    background "[bg_color]"
```

**说明:** Ren'Py的属性不能直接使用Python条件表达式，需要先在python块中计算，然后用字符串插值。

### 3. text 元素的颜色条件表达式

**❌ 错误写法:**
```renpy
text "状态" color ("#27ae60" if active else "#95a5a6")
```

**✅ 正确写法:**
```renpy
python:
    status_color = "#27ae60" if active else "#95a5a6"

text "状态" color "[status_color]"
```

### 4. if 语句在 screen 中的使用

**❌ 错误写法:**
```renpy
frame:
    if condition:
        text "内容"
```

**✅ 正确写法:**
```renpy
frame:
    if condition:
        text "内容"
    # 或者使用 vbox/hbox 包装
    vbox:
        if condition:
            text "内容"
```

**说明:** 某些容器元素对直接的if语句支持有限，建议用vbox或hbox包装。

## 修复工具使用

### 自动语法检查
```bash
python test_renpy_syntax.py
```

这个工具会检查：
- textbutton的size属性错误
- 属性中的条件表达式
- 引号匹配问题
- 基本的缩进错误

### 手动检查清单

在提交代码前，请检查：

1. **textbutton属性**
   - [ ] 所有textbutton使用`text_size`而不是`size`
   - [ ] 所有textbutton的action属性正确

2. **条件表达式**
   - [ ] 属性中的条件表达式已移到python块中
   - [ ] 使用字符串插值`"[variable]"`引用变量

3. **引号和括号**
   - [ ] 所有引号正确匹配
   - [ ] 所有括号正确匹配
   - [ ] 字符串正确转义

4. **缩进和结构**
   - [ ] Python块正确缩进
   - [ ] screen元素正确嵌套
   - [ ] if语句正确使用

## 常用修复模式

### 模式1: 条件背景色
```renpy
# 在循环或条件判断前
python:
    bg_color = "#active_color" if condition else "#inactive_color"

# 在UI元素中使用
frame:
    background "[bg_color]"
```

### 模式2: 条件文本颜色
```renpy
python:
    text_color = "#success" if success else "#error"

text "状态信息" color "[text_color]"
```

### 模式3: 条件显示内容
```renpy
vbox:
    if condition:
        text "条件为真时显示"
    else:
        text "条件为假时显示"
```

### 模式4: 动态按钮状态
```renpy
python:
    button_enabled = can_perform_action()

textbutton "执行操作":
    action Function(perform_action) if button_enabled else NullAction()
    text_size 16
```

## 调试技巧

### 1. 使用Ren'Py的错误信息
- 仔细阅读错误信息中的文件名和行号
- 注意"not a valid child"类型的错误，通常是结构问题

### 2. 分段测试
- 注释掉有问题的部分，逐步恢复
- 使用简单的text元素测试基本结构

### 3. 使用开发工具
- 启用Ren'Py的开发者模式
- 使用Shift+D打开开发者菜单
- 查看变量和屏幕状态

## 最佳实践

### 1. 代码组织
```renpy
# 在screen开始时定义所有python变量
screen my_screen():
    python:
        # 所有条件计算
        bg_color = calculate_bg_color()
        text_color = calculate_text_color()
        button_enabled = check_button_state()
    
    # UI结构
    vbox:
        frame:
            background "[bg_color]"
            text "内容" color "[text_color]"
```

### 2. 变量命名
- 使用描述性的变量名
- 颜色变量以`_color`结尾
- 状态变量以`_enabled`、`_active`等结尾

### 3. 注释说明
```renpy
python:
    # 根据NPC状态计算背景色
    npc_bg_color = "#34495e" if npc.is_active else "#2c3e50"
    # 根据NPC状态计算状态指示器颜色
    status_color = "#27ae60" if npc.is_active else "#95a5a6"
```

## 版本兼容性

### Ren'Py 7.4+
- 支持`text_size`属性
- 更好的条件表达式支持
- 改进的错误信息

### Ren'Py 8.0+
- 更严格的语法检查
- 更好的Python 3支持
- 新的UI元素

## 故障排除

### 问题: "Parsing the script failed"
1. 检查最近修改的文件
2. 运行语法检查工具
3. 查看具体的错误行号
4. 检查引号、括号匹配

### 问题: "not a valid child of the frame statement"
1. 检查frame内的元素结构
2. 确保if语句正确嵌套
3. 考虑使用vbox/hbox包装

### 问题: 变量未定义
1. 确保在python块中定义变量
2. 检查变量作用域
3. 使用字符串插值语法

---

**提示:** 使用`python test_renpy_syntax.py`定期检查语法，可以避免大部分常见错误。
