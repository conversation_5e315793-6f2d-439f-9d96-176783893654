#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
M2阶段系统测试
测试多NPC经济结算和动态价格系统
"""

import sys
import os
import random
from datetime import datetime

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

def test_m2_system():
    """测试M2阶段的经济系统功能"""
    print("=" * 60)
    print("M2阶段系统测试：多NPC经济结算 + 动态价格")
    print("=" * 60)
    
    try:
        # 导入核心模块
        from core import DatabaseManager, WorldGenerator, NPCGenerator
        from core.economy_system import EconomySystem
        from core.market_events import MarketEventSystem
        from core.time_system import TimeSystem
        
        print("✓ 所有核心模块导入成功")
        
        # 1. 初始化系统
        print("\n1. 初始化系统...")
        db_manager = DatabaseManager("test_m2.db")
        world_generator = WorldGenerator(db_manager)
        npc_generator = NPCGenerator(db_manager)
        economy_system = EconomySystem(db_manager)
        market_event_system = MarketEventSystem()
        time_system = TimeSystem(db_manager)
        
        print("✓ 系统初始化完成")
        
        # 2. 创建测试世界
        print("\n2. 创建测试世界...")
        world = world_generator.generate_world("M2测试世界")
        print(f"✓ 世界创建成功: {world.name}")
        
        # 3. 生成测试NPC
        print("\n3. 生成测试NPC...")
        test_npcs = []
        for i in range(20):  # 生成20个NPC进行测试
            npc = npc_generator.generate_npc()
            test_npcs.append(npc)
            db_manager.save_npc(npc)
        
        print(f"✓ 生成了 {len(test_npcs)} 个测试NPC")
        
        # 4. 测试动态价格系统
        print("\n4. 测试动态价格系统...")
        
        # 显示初始商品价格
        print("  初始商品价格样本:")
        sample_products = list(economy_system.products.values())[:5]
        for product in sample_products:
            market_data = economy_system.market_data.get(product.id)
            if market_data:
                current_price = product.get_market_price(market_data)
                print(f"    {product.name}: 基础价格 ¥{product.base_price:.0f} -> 当前价格 ¥{current_price:.0f}")
        
        # 5. 测试市场事件系统
        print("\n5. 测试市场事件系统...")
        
        # 生成几个测试事件
        for season in ["spring", "summer", "autumn", "winter"]:
            event = market_event_system.generate_random_event(season)
            if event:
                market_event_system.activate_event(event)
                print(f"  生成事件: {event.name} ({season})")
                print(f"    影响: {event.get_impact_description()}")
        
        active_events = market_event_system.get_active_events()
        print(f"✓ 当前活跃事件数量: {len(active_events)}")
        
        # 6. 测试多NPC经济交互
        print("\n6. 测试多NPC经济交互...")
        
        # 给NPC分配工作和收入
        for i, npc in enumerate(test_npcs):
            if i < 15:  # 75%的NPC有工作
                npc.monthly_income = random.randint(2000, 8000)
                npc.cash = random.randint(500, 2000)
                npc.bank_balance = random.randint(1000, 10000)
        
        # 模拟多轮经济活动
        print("  模拟经济活动...")
        total_transactions = 0
        
        for round_num in range(5):
            print(f"    第 {round_num + 1} 轮:")
            
            # 处理消费
            round_transactions = 0
            for npc in test_npcs:
                if npc.monthly_income > 0:
                    transactions = economy_system.process_consumption(npc)
                    round_transactions += len(transactions)
                    total_transactions += len(transactions)
            
            print(f"      本轮交易数: {round_transactions}")
            
            # 模拟市场波动
            economy_system.simulate_market_fluctuations()
            
            # 应用市场事件影响
            for product_id, market_data in economy_system.market_data.items():
                product = economy_system.products.get(product_id)
                if product:
                    price_mult, supply_mult, demand_mult = market_event_system.get_category_multipliers(product.category)
                    if price_mult != 1.0 or supply_mult != 1.0 or demand_mult != 1.0:
                        market_data.current_supply *= supply_mult
                        market_data.current_demand *= demand_mult
        
        print(f"✓ 总交易数: {total_transactions}")
        
        # 7. 测试价格变化
        print("\n7. 测试价格变化...")
        print("  价格变化样本:")
        for product in sample_products:
            market_data = economy_system.market_data.get(product.id)
            if market_data:
                new_price = product.get_market_price(market_data)
                price_change = ((new_price - product.base_price) / product.base_price * 100) if product.base_price > 0 else 0
                print(f"    {product.name}: ¥{product.base_price:.0f} -> ¥{new_price:.0f} ({price_change:+.1f}%)")
        
        # 8. 测试市场分析
        print("\n8. 测试市场分析...")
        market_analysis = economy_system.get_market_analysis()
        
        print("  市场概况:")
        print(f"    总商品数: {market_analysis['total_products']}")
        print(f"    今日交易数: {market_analysis['total_transactions_today']}")
        print(f"    当前季节: {market_analysis['current_season']}")
        
        print("  类别分析:")
        for category, stats in market_analysis.get('category_analysis', {}).items():
            print(f"    {category}: 平均价格 ¥{stats['avg_price']:.0f}, 供应 {stats['total_supply']:.0f}, 需求 {stats['total_demand']:.0f}")
        
        # 9. 测试时间系统集成
        print("\n9. 测试时间系统集成...")
        
        # 推进时间并观察经济变化
        for i in range(3):
            print(f"  推进时间 {i+1}:")
            results = time_system.advance_time(4)  # 推进4小时
            
            if results.get("economic_updates"):
                print(f"    经济更新: {len(results['economic_updates'])} 项")
                for update in results["economic_updates"][:3]:  # 显示前3项
                    print(f"      - {update}")
            
            if results.get("market_events"):
                print(f"    市场事件: {len(results['market_events'])} 项")
                for event in results["market_events"]:
                    print(f"      - {event}")
        
        # 10. 生成最终报告
        print("\n10. 最终系统状态...")
        
        economic_summary = economy_system.get_economic_summary()
        print("  经济摘要:")
        print(f"    总现金: ¥{economic_summary['total_cash']:,.0f}")
        print(f"    月总收入: ¥{economic_summary['total_monthly_income']:,.0f}")
        print(f"    供需比: {economic_summary['supply_demand_ratio']:.2f}")
        print(f"    平均价格变化: {economic_summary['avg_price_change']:.1f}%")
        print(f"    市场活跃度: {economic_summary['market_activity']}")
        
        event_summary = market_event_system.get_event_summary()
        print(f"  活跃市场事件: {event_summary['active_events_count']} 个")
        
        time_info = time_system.get_current_time_info()
        print(f"  当前时间: {time_info['time_string']}")
        print(f"  经济活跃度: {time_info['economic_activity']}")
        
        print("\n" + "=" * 60)
        print("✅ M2阶段系统测试完成！")
        print("✅ 多NPC经济结算系统正常工作")
        print("✅ 动态价格系统正常工作")
        print("✅ 市场事件系统正常工作")
        print("✅ 时间系统集成正常工作")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_m2_system()
    sys.exit(0 if success else 1)
