# -*- coding: utf-8 -*-
"""
行为系统
实现NPC意图生成、行为调度器和冲突解决机制
"""

import random
import math
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
from .data_models import NPC, Activity, ActivityType, Goal, generate_uuid
from .database import DatabaseManager


class IntentionType(Enum):
    """意图类型"""
    BASIC_NEED = "basic_need"      # 基本需求（吃饭、睡觉等）
    WORK = "work"                  # 工作相关
    SOCIAL = "social"              # 社交活动
    PERSONAL = "personal"          # 个人发展
    ENTERTAINMENT = "entertainment" # 娱乐休闲
    EMERGENCY = "emergency"        # 紧急情况


@dataclass
class Intention:
    """意图对象"""
    id: str
    npc_id: str
    type: IntentionType
    description: str
    priority: float  # 优先级 0-100
    urgency: float   # 紧急程度 0-100
    target_activity: ActivityType
    requirements: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    expected_duration: int = 60  # 预期持续时间（分钟）
    created_at: str = ""
    
    def get_score(self) -> float:
        """计算意图得分（用于排序）"""
        return (self.priority * 0.6 + self.urgency * 0.4)


@dataclass
class ActionPlan:
    """行动计划"""
    npc_id: str
    intentions: List[Intention] = field(default_factory=list)
    scheduled_activities: Dict[int, Activity] = field(default_factory=dict)  # hour -> Activity
    conflicts: List[str] = field(default_factory=list)
    
    def add_intention(self, intention: Intention):
        """添加意图"""
        self.intentions.append(intention)
        self.intentions.sort(key=lambda x: x.get_score(), reverse=True)
    
    def has_conflict(self, hour: int) -> bool:
        """检查指定时间是否有冲突"""
        return hour in self.scheduled_activities


class BehaviorSystem:
    """行为系统管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.active_plans: Dict[str, ActionPlan] = {}  # npc_id -> ActionPlan
        
        # 基本需求阈值
        self.need_thresholds = {
            "hunger": 70.0,      # 饥饿值超过70时需要进食
            "energy": 30.0,      # 精力低于30时需要休息
            "stress": 80.0,      # 压力超过80时需要放松
            "social": 20.0,      # 社交需求低于20时需要社交
            "happiness": 30.0    # 幸福度低于30时需要娱乐
        }
    
    def generate_intentions(self, npc: NPC, current_hour: int) -> List[Intention]:
        """为NPC生成意图"""
        intentions = []
        
        # 1. 基本需求意图
        basic_intentions = self._generate_basic_need_intentions(npc)
        intentions.extend(basic_intentions)
        
        # 2. 工作相关意图
        if npc.current_job and npc.current_job.is_active:
            work_intentions = self._generate_work_intentions(npc, current_hour)
            intentions.extend(work_intentions)
        
        # 3. 社交意图
        social_intentions = self._generate_social_intentions(npc)
        intentions.extend(social_intentions)
        
        # 4. 个人发展意图
        personal_intentions = self._generate_personal_intentions(npc)
        intentions.extend(personal_intentions)
        
        # 5. 娱乐意图
        entertainment_intentions = self._generate_entertainment_intentions(npc)
        intentions.extend(entertainment_intentions)
        
        # 根据NPC性格调整意图优先级
        self._adjust_intentions_by_personality(npc, intentions)
        
        return intentions
    
    def _generate_basic_need_intentions(self, npc: NPC) -> List[Intention]:
        """生成基本需求意图"""
        intentions = []
        
        # 饥饿
        if npc.daily_stats.hunger > self.need_thresholds["hunger"]:
            urgency = min(100, (npc.daily_stats.hunger - 50) * 2)
            intention = Intention(
                id=generate_uuid(),
                npc_id=npc.id,
                type=IntentionType.BASIC_NEED,
                description="需要进食",
                priority=90.0,
                urgency=urgency,
                target_activity=ActivityType.EAT,
                expected_duration=60
            )
            intentions.append(intention)
        
        # 精力不足
        if npc.daily_stats.energy < self.need_thresholds["energy"]:
            urgency = min(100, (50 - npc.daily_stats.energy) * 2)
            intention = Intention(
                id=generate_uuid(),
                npc_id=npc.id,
                type=IntentionType.BASIC_NEED,
                description="需要休息",
                priority=85.0,
                urgency=urgency,
                target_activity=ActivityType.REST,
                expected_duration=120
            )
            intentions.append(intention)
        
        # 压力过大
        if npc.daily_stats.stress > self.need_thresholds["stress"]:
            urgency = min(100, (npc.daily_stats.stress - 50) * 1.5)
            intention = Intention(
                id=generate_uuid(),
                npc_id=npc.id,
                type=IntentionType.BASIC_NEED,
                description="需要放松",
                priority=75.0,
                urgency=urgency,
                target_activity=ActivityType.ENTERTAINMENT,
                expected_duration=90
            )
            intentions.append(intention)
        
        return intentions
    
    def _generate_work_intentions(self, npc: NPC, current_hour: int) -> List[Intention]:
        """生成工作相关意图"""
        intentions = []
        
        # 工作时间内的工作意图
        work_hours = npc.current_job.work_hours_per_day
        work_start = 9  # 假设9点开始工作
        
        if work_start <= current_hour < work_start + work_hours:
            intention = Intention(
                id=generate_uuid(),
                npc_id=npc.id,
                type=IntentionType.WORK,
                description="执行工作任务",
                priority=80.0,
                urgency=60.0,
                target_activity=ActivityType.WORK,
                expected_duration=60,
                requirements={"location": npc.work_location}
            )
            intentions.append(intention)
        
        # 通勤意图
        if current_hour == work_start - 1 or current_hour == work_start + work_hours:
            intention = Intention(
                id=generate_uuid(),
                npc_id=npc.id,
                type=IntentionType.WORK,
                description="通勤",
                priority=85.0,
                urgency=70.0,
                target_activity=ActivityType.COMMUTE,
                expected_duration=60
            )
            intentions.append(intention)
        
        return intentions
    
    def _generate_social_intentions(self, npc: NPC) -> List[Intention]:
        """生成社交意图"""
        intentions = []
        
        # 社交需求
        if npc.daily_stats.social_need < self.need_thresholds["social"]:
            priority = 60.0
            if npc.personality.extraversion > 60:
                priority += 20.0
            
            intention = Intention(
                id=generate_uuid(),
                npc_id=npc.id,
                type=IntentionType.SOCIAL,
                description="社交活动",
                priority=priority,
                urgency=40.0,
                target_activity=ActivityType.SOCIALIZE,
                expected_duration=120
            )
            intentions.append(intention)
        
        return intentions
    
    def _generate_personal_intentions(self, npc: NPC) -> List[Intention]:
        """生成个人发展意图"""
        intentions = []
        
        # 学习意图（基于目标和性格）
        if npc.personality.openness > 60:
            for goal in npc.goals:
                if goal.is_active and "学习" in goal.description or "技能" in goal.description:
                    intention = Intention(
                        id=generate_uuid(),
                        npc_id=npc.id,
                        type=IntentionType.PERSONAL,
                        description=f"学习提升：{goal.description}",
                        priority=goal.priority,
                        urgency=30.0,
                        target_activity=ActivityType.STUDY,
                        expected_duration=90
                    )
                    intentions.append(intention)
                    break  # 一次只处理一个学习目标
        
        # 运动意图
        if npc.daily_stats.health < 80 and random.random() < 0.3:
            priority = 50.0
            if npc.personality.conscientiousness > 60:
                priority += 15.0
            
            intention = Intention(
                id=generate_uuid(),
                npc_id=npc.id,
                type=IntentionType.PERSONAL,
                description="运动健身",
                priority=priority,
                urgency=25.0,
                target_activity=ActivityType.EXERCISE,
                expected_duration=90
            )
            intentions.append(intention)
        
        return intentions
    
    def _generate_entertainment_intentions(self, npc: NPC) -> List[Intention]:
        """生成娱乐意图"""
        intentions = []
        
        # 娱乐需求
        if npc.daily_stats.happiness < self.need_thresholds["happiness"]:
            priority = 55.0
            if npc.personality.extraversion > 60:
                priority += 10.0
            
            intention = Intention(
                id=generate_uuid(),
                npc_id=npc.id,
                type=IntentionType.ENTERTAINMENT,
                description="娱乐放松",
                priority=priority,
                urgency=35.0,
                target_activity=ActivityType.ENTERTAINMENT,
                expected_duration=120
            )
            intentions.append(intention)
        
        # 购物意图
        if npc.cash > 1000 and random.random() < 0.2:
            priority = 40.0
            if npc.consumption_preferences.get("shopping", 50) > 70:
                priority += 15.0
            
            intention = Intention(
                id=generate_uuid(),
                npc_id=npc.id,
                type=IntentionType.ENTERTAINMENT,
                description="购物",
                priority=priority,
                urgency=20.0,
                target_activity=ActivityType.SHOPPING,
                expected_duration=120
            )
            intentions.append(intention)
        
        return intentions
    
    def _adjust_intentions_by_personality(self, npc: NPC, intentions: List[Intention]):
        """根据NPC性格调整意图优先级"""
        for intention in intentions:
            # 尽责性影响工作和个人发展意图
            if intention.type in [IntentionType.WORK, IntentionType.PERSONAL]:
                if npc.personality.conscientiousness > 60:
                    intention.priority += 10.0
                elif npc.personality.conscientiousness < 40:
                    intention.priority -= 10.0
            
            # 外向性影响社交和娱乐意图
            if intention.type in [IntentionType.SOCIAL, IntentionType.ENTERTAINMENT]:
                if npc.personality.extraversion > 60:
                    intention.priority += 15.0
                elif npc.personality.extraversion < 40:
                    intention.priority -= 15.0
            
            # 神经质影响基本需求的紧急程度
            if intention.type == IntentionType.BASIC_NEED:
                if npc.personality.neuroticism > 60:
                    intention.urgency += 10.0
            
            # 确保优先级在合理范围内
            intention.priority = max(0, min(100, intention.priority))
            intention.urgency = max(0, min(100, intention.urgency))
    
    def create_action_plan(self, npc: NPC, intentions: List[Intention], current_hour: int) -> ActionPlan:
        """创建行动计划"""
        plan = ActionPlan(npc.id)
        
        # 添加所有意图
        for intention in intentions:
            plan.add_intention(intention)
        
        # 尝试安排活动
        self._schedule_activities(plan, current_hour)
        
        return plan
    
    def _schedule_activities(self, plan: ActionPlan, current_hour: int):
        """安排活动时间"""
        # 按优先级排序的意图
        sorted_intentions = sorted(plan.intentions, key=lambda x: x.get_score(), reverse=True)
        
        for intention in sorted_intentions:
            # 寻找合适的时间段
            best_hour = self._find_best_time_slot(plan, intention, current_hour)
            
            if best_hour is not None:
                # 创建活动
                activity = self._create_activity_from_intention(intention)
                plan.scheduled_activities[best_hour] = activity
            else:
                # 记录冲突
                plan.conflicts.append(f"无法安排活动：{intention.description}")
    
    def _find_best_time_slot(self, plan: ActionPlan, intention: Intention, current_hour: int) -> Optional[int]:
        """寻找最佳时间段"""
        # 根据活动类型确定合适的时间范围
        if intention.target_activity == ActivityType.WORK:
            time_range = range(9, 17)  # 工作时间
        elif intention.target_activity == ActivityType.EAT:
            # 用餐时间
            meal_times = [7, 12, 18]
            for meal_time in meal_times:
                if not plan.has_conflict(meal_time):
                    return meal_time
            return None
        elif intention.target_activity == ActivityType.SLEEP:
            time_range = range(22, 24)  # 睡觉时间
        else:
            # 其他活动在空闲时间
            time_range = range(current_hour, 22)
        
        # 寻找空闲时间段
        for hour in time_range:
            if not plan.has_conflict(hour):
                return hour
        
        return None
    
    def _create_activity_from_intention(self, intention: Intention) -> Activity:
        """从意图创建活动"""
        activity_templates = {
            ActivityType.EAT: {"energy_cost": -15.0, "happiness_gain": 8.0, "stress_change": -5.0},
            ActivityType.WORK: {"energy_cost": 25.0, "happiness_gain": -2.0, "stress_change": 10.0},
            ActivityType.REST: {"energy_cost": -30.0, "happiness_gain": 10.0, "stress_change": -20.0},
            ActivityType.SOCIALIZE: {"energy_cost": 10.0, "happiness_gain": 20.0, "stress_change": -15.0},
            ActivityType.ENTERTAINMENT: {"energy_cost": 5.0, "happiness_gain": 25.0, "stress_change": -20.0},
            ActivityType.STUDY: {"energy_cost": 20.0, "happiness_gain": 5.0, "stress_change": 5.0},
            ActivityType.EXERCISE: {"energy_cost": 30.0, "happiness_gain": 15.0, "stress_change": -10.0},
            ActivityType.SHOPPING: {"energy_cost": 15.0, "happiness_gain": 12.0, "stress_change": -8.0},
            ActivityType.COMMUTE: {"energy_cost": 10.0, "happiness_gain": -5.0, "stress_change": 5.0}
        }
        
        template = activity_templates.get(intention.target_activity, {})
        
        return Activity(
            id=generate_uuid(),
            type=intention.target_activity,
            name=intention.description,
            duration=intention.expected_duration,
            energy_cost=template.get("energy_cost", 0.0),
            happiness_gain=template.get("happiness_gain", 0.0),
            stress_change=template.get("stress_change", 0.0),
            location=intention.requirements.get("location"),
            requirements=intention.requirements,
            rewards={}
        )
    
    def resolve_conflicts(self, plans: List[ActionPlan]) -> List[str]:
        """解决多个NPC之间的冲突"""
        conflicts = []
        
        # 这里可以实现更复杂的冲突解决逻辑
        # 例如：资源竞争、位置冲突等
        
        return conflicts
    
    def get_plan_for_npc(self, npc_id: str) -> Optional[ActionPlan]:
        """获取NPC的行动计划"""
        return self.active_plans.get(npc_id)
    
    def update_plan(self, npc_id: str, plan: ActionPlan):
        """更新NPC的行动计划"""
        self.active_plans[npc_id] = plan
