# -*- coding: utf-8 -*-
"""
核心数据模型
定义游戏世界的基础数据结构，包括地理、行政、经济等核心实体
"""

import json
try:
    import sqlite3
    SQLITE_AVAILABLE = True
except ImportError:
    SQLITE_AVAILABLE = False
    sqlite3 = None

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid


class RegionType(Enum):
    """行政区划类型"""
    WORLD = "world"
    COUNTRY = "country"
    PROVINCE = "province"
    CITY = "city"
    DISTRICT = "district"
    VILLAGE = "village"


class BuildingType(Enum):
    """建筑类型"""
    RESIDENTIAL = "residential"  # 住宅
    COMMERCIAL = "commercial"    # 商业
    INDUSTRIAL = "industrial"    # 工业
    OFFICE = "office"           # 办公
    SCHOOL = "school"           # 学校
    HOSPITAL = "hospital"       # 医院
    GOVERNMENT = "government"   # 政府
    ENTERTAINMENT = "entertainment"  # 娱乐


class OrganizationType(Enum):
    """组织类型"""
    COMPANY = "company"
    SCHOOL = "school"
    GOVERNMENT = "government"
    GANG = "gang"
    NGO = "ngo"


class Gender(Enum):
    """性别"""
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"


class EducationLevel(Enum):
    """教育水平"""
    NONE = "none"
    PRIMARY = "primary"
    SECONDARY = "secondary"
    HIGH_SCHOOL = "high_school"
    COLLEGE = "college"
    UNIVERSITY = "university"
    GRADUATE = "graduate"
    DOCTORATE = "doctorate"


class JobType(Enum):
    """职业类型"""
    UNEMPLOYED = "unemployed"
    STUDENT = "student"
    WORKER = "worker"
    CLERK = "clerk"
    MANAGER = "manager"
    PROFESSIONAL = "professional"
    ENTREPRENEUR = "entrepreneur"
    GOVERNMENT = "government"
    RETIRED = "retired"


class ActivityType(Enum):
    """活动类型"""
    SLEEP = "sleep"
    WORK = "work"
    EAT = "eat"
    STUDY = "study"
    EXERCISE = "exercise"
    SOCIALIZE = "socialize"
    ENTERTAINMENT = "entertainment"
    SHOPPING = "shopping"
    COMMUTE = "commute"
    REST = "rest"


@dataclass
class Policy:
    """政策对象"""
    id: str
    name: str
    description: str
    type: str  # 税收、基建、教育等
    value: float
    effective_date: str
    expiry_date: Optional[str] = None
    target_region: Optional[str] = None


@dataclass
class Skill:
    """技能对象"""
    name: str
    level: float  # 0-100
    experience: float = 0.0
    category: str = "general"  # 技能分类


@dataclass
class PersonalityTrait:
    """性格特质（Big Five模型）"""
    openness: float = 50.0          # 开放性 0-100
    conscientiousness: float = 50.0  # 尽责性 0-100
    extraversion: float = 50.0       # 外向性 0-100
    agreeableness: float = 50.0      # 宜人性 0-100
    neuroticism: float = 50.0        # 神经质 0-100


@dataclass
class DailyStats:
    """日常状态"""
    energy: float = 100.0      # 精力值 0-100
    happiness: float = 50.0    # 幸福度 0-100
    stress: float = 0.0        # 压力值 0-100
    health: float = 100.0      # 健康值 0-100
    hunger: float = 0.0        # 饥饿值 0-100
    social_need: float = 50.0  # 社交需求 0-100


@dataclass
class Goal:
    """目标对象"""
    id: str
    description: str
    type: str  # short_term, long_term
    priority: float = 50.0  # 优先级 0-100
    progress: float = 0.0   # 进度 0-100
    deadline: Optional[str] = None
    is_active: bool = True


@dataclass
class Activity:
    """活动对象"""
    id: str
    type: ActivityType
    name: str
    duration: int  # 持续时间（分钟）
    energy_cost: float = 0.0
    happiness_gain: float = 0.0
    stress_change: float = 0.0
    location: Optional[str] = None  # 建筑ID
    requirements: Dict[str, Any] = field(default_factory=dict)
    rewards: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Relationship:
    """对外关系"""
    target_region_id: str
    relation_type: str  # 友好、敌对、中立等
    value: float  # -100 到 100
    trade_volume: float = 0.0
    last_updated: str = ""


@dataclass
class NPCRelationship:
    """NPC之间的关系"""
    target_npc_id: str
    relation_type: str  # 朋友、家人、同事、恋人、敌人等
    intimacy: float = 0.0  # 亲密度 -100 到 100
    trust: float = 0.0     # 信任度 -100 到 100
    last_interaction: str = ""
    interaction_count: int = 0


@dataclass
class Job:
    """工作对象"""
    id: str
    title: str
    job_type: JobType
    organization_id: str
    building_id: str
    salary: float
    work_hours_per_day: int = 8
    required_skills: List[str] = field(default_factory=list)
    work_schedule: Dict[str, Any] = field(default_factory=dict)  # 工作时间表
    is_active: bool = True


@dataclass
class NPC:
    """非玩家角色"""
    id: str
    name: str

    # 基础属性
    age: int
    gender: Gender
    education_level: EducationLevel
    birth_date: str
    birth_place: str  # Block ID
    current_residence: str  # Building ID

    # 家庭关系
    family_members: List[str] = field(default_factory=list)  # NPC IDs
    marital_status: str = "single"  # single, married, divorced, widowed

    # 经济属性
    cash: float = 1000.0
    bank_balance: float = 5000.0
    assets: Dict[str, float] = field(default_factory=dict)  # 资产
    debts: Dict[str, float] = field(default_factory=dict)   # 债务
    monthly_income: float = 0.0
    monthly_expenses: float = 0.0

    # 职业信息
    current_job: Optional[Job] = None
    job_history: List[Job] = field(default_factory=list)

    # 心理属性
    personality: PersonalityTrait = field(default_factory=PersonalityTrait)
    values: Dict[str, float] = field(default_factory=dict)  # 价值观
    goals: List[Goal] = field(default_factory=list)

    # 技能系统
    skills: Dict[str, Skill] = field(default_factory=dict)

    # 日常状态
    daily_stats: DailyStats = field(default_factory=DailyStats)

    # 社交关系
    relationships: List[NPCRelationship] = field(default_factory=list)

    # 行为历史
    activity_history: List[Activity] = field(default_factory=list)
    current_activity: Optional[Activity] = None

    # 消费偏好
    consumption_preferences: Dict[str, float] = field(default_factory=dict)

    # 位置信息
    current_location: str = ""  # Building ID
    home_location: str = ""     # Building ID
    work_location: str = ""     # Building ID

    # 时间管理
    daily_schedule: Dict[int, str] = field(default_factory=dict)  # 小时 -> 活动类型

    # 状态标记
    is_active: bool = True
    is_player_controlled: bool = False

    # 元数据
    created_at: str = ""
    updated_at: str = ""

    def get_age_group(self) -> str:
        """获取年龄组"""
        if self.age < 18:
            return "child"
        elif self.age < 30:
            return "young_adult"
        elif self.age < 50:
            return "adult"
        elif self.age < 65:
            return "middle_aged"
        else:
            return "elderly"

    def get_skill_level(self, skill_name: str) -> float:
        """获取技能等级"""
        return self.skills.get(skill_name, Skill(skill_name, 0.0)).level

    def add_skill_experience(self, skill_name: str, exp: float):
        """增加技能经验"""
        if skill_name not in self.skills:
            self.skills[skill_name] = Skill(skill_name, 0.0)

        skill = self.skills[skill_name]
        skill.experience += exp
        # 简单的等级计算：每100经验提升1级
        skill.level = min(100.0, skill.experience / 100.0)

    def get_relationship_with(self, npc_id: str) -> Optional[NPCRelationship]:
        """获取与特定NPC的关系"""
        for rel in self.relationships:
            if rel.target_npc_id == npc_id:
                return rel
        return None

    def update_daily_stats(self, **kwargs):
        """更新日常状态"""
        for key, value in kwargs.items():
            if hasattr(self.daily_stats, key):
                current_value = getattr(self.daily_stats, key)
                new_value = max(0.0, min(100.0, current_value + value))
                setattr(self.daily_stats, key, new_value)

    def can_afford(self, amount: float) -> bool:
        """检查是否能负担得起某个金额"""
        return (self.cash + self.bank_balance) >= amount

    def spend_money(self, amount: float, description: str = "") -> bool:
        """花费金钱"""
        if not self.can_afford(amount):
            return False

        if self.cash >= amount:
            self.cash -= amount
        else:
            remaining = amount - self.cash
            self.cash = 0.0
            self.bank_balance -= remaining

        return True

    def earn_money(self, amount: float, description: str = ""):
        """赚取金钱"""
        self.cash += amount


@dataclass
class Region:
    """行政区域（国/省/市）"""
    id: str  # 层级编码，如 01-05-12
    name: str
    type: RegionType
    world_id: str  # 所属世界ID
    parent_id: Optional[str] = None
    children_ids: List[str] = field(default_factory=list)
    
    # 政治经济属性
    policies: List[Policy] = field(default_factory=list)
    gov_balance: float = 0.0
    tax_rate: float = 0.1
    population: int = 0
    gdp: float = 0.0
    
    # 对外关系
    relationships: List[Relationship] = field(default_factory=list)
    
    # 基础设施
    infrastructure_score: float = 50.0
    education_level: float = 50.0
    healthcare_level: float = 50.0
    
    # 元数据
    created_at: str = ""
    updated_at: str = ""


@dataclass
class Block:
    """区块（区/村）- 具体的地理单元"""
    id: str  # 层级编码，如 01-05-12-04-09
    name: str
    type: RegionType  # DISTRICT 或 VILLAGE
    world_id: str  # 所属世界ID
    parent_id: str  # 所属市的ID
    
    # 地理属性
    land_area: float  # 平方公里
    buildable_index_max: float  # 建筑容量指数上限
    buildable_index_used: float = 0.0  # 已使用的建筑容量
    
    # 人口与基础设施
    population: int = 0
    population_cap: int = 0  # 人口容量上限
    infrastructure_score: float = 50.0
    
    # 建筑列表
    buildings: List[str] = field(default_factory=list)  # Building IDs
    
    # 经济属性
    land_price: float = 1000.0  # 每平方米价格
    development_level: float = 1.0  # 发展水平
    
    # 元数据
    created_at: str = ""
    updated_at: str = ""


@dataclass
class Building:
    """建筑物"""
    id: str
    name: str
    type: BuildingType
    block_id: str  # 所在区块
    
    # 建筑属性
    floor_area: float  # 建筑面积
    capacity_index: float  # 占用的建筑容量指数
    max_occupants: int  # 最大入驻单位数
    
    # 入驻情况
    organizations: List[str] = field(default_factory=list)  # Organization IDs
    residents: List[str] = field(default_factory=list)  # NPC IDs (for residential)
    
    # 经济属性
    construction_cost: float = 0.0
    maintenance_cost: float = 0.0
    rent_price: float = 0.0
    
    # 状态
    condition: float = 100.0  # 建筑状况 0-100
    is_active: bool = True
    
    # 元数据
    created_at: str = ""
    updated_at: str = ""


@dataclass
class Organization:
    """组织（公司/学校/政府部门等）"""
    id: str
    name: str
    type: OrganizationType
    
    # 基础属性
    capital: float = 0.0  # 资本
    reputation: float = 50.0  # 声誉
    
    # 员工与管理
    employees: List[str] = field(default_factory=list)  # NPC IDs
    max_employees: int = 10
    
    # 多地布局
    branches: List[str] = field(default_factory=list)  # Building IDs
    headquarters: Optional[str] = None  # Building ID
    
    # 组织政策
    policies: List[Policy] = field(default_factory=list)
    
    # 业务属性
    industry: str = "general"
    business_model: str = ""
    monthly_revenue: float = 0.0
    monthly_expenses: float = 0.0
    
    # 状态
    is_active: bool = True
    founded_date: str = ""
    
    # 元数据
    created_at: str = ""
    updated_at: str = ""


@dataclass
class World:
    """世界实例 - 顶层容器"""
    id: str
    name: str
    version: str = "1.0"
    
    # 世界设置
    total_population: int = 0
    total_regions: int = 0
    total_blocks: int = 0
    
    # 全局经济参数
    global_inflation_rate: float = 0.02
    global_growth_rate: float = 0.03
    base_currency: str = "credits"
    
    # 时间系统
    current_day: int = 1
    current_season: str = "spring"
    current_year: int = 2024
    
    # 世界状态
    world_events: List[Dict[str, Any]] = field(default_factory=list)
    global_policies: List[Policy] = field(default_factory=list)
    
    # 元数据
    created_at: str = ""
    updated_at: str = ""
    
    def get_hierarchical_id(self, level: int, indices: List[int]) -> str:
        """生成层级编码ID"""
        return "-".join([f"{idx:02d}" for idx in indices[:level]])
    
    def parse_hierarchical_id(self, region_id: str) -> List[int]:
        """解析层级编码ID"""
        return [int(part) for part in region_id.split("-")]


# 工具函数
def generate_uuid() -> str:
    """生成唯一ID"""
    return str(uuid.uuid4())


def get_region_level(region_id: str) -> int:
    """根据ID获取区域层级"""
    return len(region_id.split("-"))


def get_parent_id(region_id: str) -> Optional[str]:
    """获取父级区域ID"""
    parts = region_id.split("-")
    if len(parts) <= 1:
        return None
    return "-".join(parts[:-1])


# 数据验证函数
def validate_hierarchical_id(region_id: str) -> bool:
    """验证层级编码ID格式"""
    try:
        parts = region_id.split("-")
        for part in parts:
            if not part.isdigit() or len(part) != 2:
                return False
        return True
    except:
        return False


def validate_region_hierarchy(regions: List[Region]) -> bool:
    """验证区域层级关系的完整性"""
    region_dict = {r.id: r for r in regions}
    
    for region in regions:
        # 检查父子关系
        if region.parent_id:
            if region.parent_id not in region_dict:
                return False
            parent = region_dict[region.parent_id]
            if region.id not in parent.children_ids:
                return False
        
        # 检查子级关系
        for child_id in region.children_ids:
            if child_id not in region_dict:
                return False
            child = region_dict[child_id]
            if child.parent_id != region.id:
                return False
    
    return True
