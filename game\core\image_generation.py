# -*- coding: utf-8 -*-
"""
文生图立绘系统
支持多种图像生成模型，为NPC生成个性化立绘和场景图片
"""

import os
import json
import hashlib
import base64
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import threading
import queue
import time
from PIL import Image
import io

from .data_models import NPC, Gender, generate_uuid


class ImageProvider(Enum):
    """图像生成服务提供商"""
    STABLE_DIFFUSION = "stable_diffusion"
    DALLE = "dalle"
    MIDJOURNEY = "midjourney"
    FIREFLY = "firefly"
    LOCAL = "local"
    MOCK = "mock"


class ImageStyle(Enum):
    """图像风格"""
    ANIME = "anime"
    REALISTIC = "realistic"
    CARTOON = "cartoon"
    PIXEL_ART = "pixel_art"
    WATERCOLOR = "watercolor"
    OIL_PAINTING = "oil_painting"
    SKETCH = "sketch"
    CYBERPUNK = "cyberpunk"
    FANTASY = "fantasy"
    MINIMALIST = "minimalist"


class ImageType(Enum):
    """图像类型"""
    PORTRAIT = "portrait"        # 人物肖像
    FULL_BODY = "full_body"     # 全身像
    SCENE = "scene"             # 场景图
    BACKGROUND = "background"    # 背景图
    ICON = "icon"               # 图标
    EXPRESSION = "expression"    # 表情图


@dataclass
class ImageGenerationRequest:
    """图像生成请求"""
    prompt: str
    negative_prompt: str = ""
    style: ImageStyle = ImageStyle.ANIME
    image_type: ImageType = ImageType.PORTRAIT
    width: int = 512
    height: int = 512
    steps: int = 20
    guidance_scale: float = 7.5
    seed: Optional[int] = None
    provider: ImageProvider = ImageProvider.MOCK
    request_id: str = ""
    priority: int = 1  # 1-5, 5为最高优先级
    
    def __post_init__(self):
        if not self.request_id:
            self.request_id = generate_uuid()


@dataclass
class ImageGenerationResult:
    """图像生成结果"""
    request_id: str
    success: bool
    image_path: str = ""
    thumbnail_path: str = ""
    width: int = 0
    height: int = 0
    file_size: int = 0
    generation_time: float = 0.0
    error_message: str = ""
    provider: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


class ImageGenerationInterface(ABC):
    """图像生成服务接口"""
    
    @abstractmethod
    def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResult:
        """生成图像"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查服务是否可用"""
        pass
    
    @abstractmethod
    def get_supported_styles(self) -> List[ImageStyle]:
        """获取支持的风格列表"""
        pass
    
    @abstractmethod
    def get_provider_name(self) -> str:
        """获取提供商名称"""
        pass


class MockImageService(ImageGenerationInterface):
    """模拟图像生成服务"""
    
    def __init__(self, image_cache_dir: str = "image_cache"):
        self.image_cache_dir = image_cache_dir
        os.makedirs(image_cache_dir, exist_ok=True)
        
        # 支持的风格
        self.supported_styles = [
            ImageStyle.ANIME,
            ImageStyle.REALISTIC,
            ImageStyle.CARTOON,
            ImageStyle.PIXEL_ART
        ]
    
    def generate_image(self, request: ImageGenerationRequest) -> ImageGenerationResult:
        """模拟图像生成"""
        start_time = time.time()
        
        try:
            # 生成缓存文件名
            prompt_hash = hashlib.md5(request.prompt.encode('utf-8')).hexdigest()
            params_hash = hashlib.md5(str(request.__dict__).encode('utf-8')).hexdigest()
            image_filename = f"mock_{params_hash}_{prompt_hash}.png"
            image_path = os.path.join(self.image_cache_dir, image_filename)
            
            # 生成缩略图路径
            thumbnail_filename = f"thumb_{params_hash}_{prompt_hash}.png"
            thumbnail_path = os.path.join(self.image_cache_dir, thumbnail_filename)
            
            # 模拟生成图像
            if not os.path.exists(image_path):
                # 创建一个简单的彩色图像
                image = self._create_mock_image(request)
                image.save(image_path, "PNG")
                
                # 创建缩略图
                thumbnail = image.copy()
                thumbnail.thumbnail((128, 128), Image.Resampling.LANCZOS)
                thumbnail.save(thumbnail_path, "PNG")
                
                # 模拟处理时间
                time.sleep(0.5)
            
            generation_time = time.time() - start_time
            file_size = os.path.getsize(image_path) if os.path.exists(image_path) else 0
            
            return ImageGenerationResult(
                request_id=request.request_id,
                success=True,
                image_path=image_path,
                thumbnail_path=thumbnail_path,
                width=request.width,
                height=request.height,
                file_size=file_size,
                generation_time=generation_time,
                provider="mock",
                metadata={
                    "prompt": request.prompt,
                    "style": request.style.value,
                    "type": request.image_type.value
                }
            )
            
        except Exception as e:
            return ImageGenerationResult(
                request_id=request.request_id,
                success=False,
                error_message=str(e),
                provider="mock",
                generation_time=time.time() - start_time
            )
    
    def _create_mock_image(self, request: ImageGenerationRequest) -> Image.Image:
        """创建模拟图像"""
        # 根据风格选择颜色
        style_colors = {
            ImageStyle.ANIME: [(255, 182, 193), (173, 216, 230), (255, 255, 224)],
            ImageStyle.REALISTIC: [(139, 69, 19), (160, 82, 45), (210, 180, 140)],
            ImageStyle.CARTOON: [(255, 69, 0), (255, 140, 0), (255, 215, 0)],
            ImageStyle.PIXEL_ART: [(0, 255, 0), (0, 0, 255), (255, 0, 255)]
        }
        
        colors = style_colors.get(request.style, [(128, 128, 128)])
        
        # 创建渐变图像
        image = Image.new('RGB', (request.width, request.height), colors[0])
        
        # 添加一些简单的图案
        if request.image_type == ImageType.PORTRAIT:
            # 绘制一个简单的人脸轮廓
            from PIL import ImageDraw
            draw = ImageDraw.Draw(image)
            
            # 脸部轮廓
            face_x = request.width // 4
            face_y = request.height // 4
            face_w = request.width // 2
            face_h = request.height // 2
            draw.ellipse([face_x, face_y, face_x + face_w, face_y + face_h], fill=colors[1] if len(colors) > 1 else colors[0])
            
            # 眼睛
            eye_size = 20
            left_eye_x = face_x + face_w // 3 - eye_size // 2
            right_eye_x = face_x + 2 * face_w // 3 - eye_size // 2
            eye_y = face_y + face_h // 3 - eye_size // 2
            draw.ellipse([left_eye_x, eye_y, left_eye_x + eye_size, eye_y + eye_size], fill=(0, 0, 0))
            draw.ellipse([right_eye_x, eye_y, right_eye_x + eye_size, eye_y + eye_size], fill=(0, 0, 0))
            
            # 嘴巴
            mouth_x = face_x + face_w // 2 - 15
            mouth_y = face_y + 2 * face_h // 3
            draw.ellipse([mouth_x, mouth_y, mouth_x + 30, mouth_y + 15], fill=(0, 0, 0))
        
        return image
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return True
    
    def get_supported_styles(self) -> List[ImageStyle]:
        """获取支持的风格列表"""
        return self.supported_styles.copy()
    
    def get_provider_name(self) -> str:
        """获取提供商名称"""
        return "Mock Image Generation Service"


class ImageGenerationManager:
    """图像生成管理器"""
    
    def __init__(self, image_cache_dir: str = "image_cache"):
        self.image_cache_dir = image_cache_dir
        self.services: Dict[ImageProvider, ImageGenerationInterface] = {}
        self.default_provider = ImageProvider.MOCK
        self.fallback_provider = ImageProvider.MOCK
        
        # 图像缓存
        self.image_cache: Dict[str, ImageGenerationResult] = {}
        
        # 请求队列和工作线程
        self.request_queue = queue.PriorityQueue()
        self.result_callbacks: Dict[str, callable] = {}
        self.worker_thread = None
        self.is_running = False
        
        # 初始化默认服务
        self.services[ImageProvider.MOCK] = MockImageService(image_cache_dir)
        
        # NPC立绘缓存
        self.npc_portraits: Dict[str, str] = {}  # npc_id -> image_path
        
        os.makedirs(image_cache_dir, exist_ok=True)
    
    def register_service(self, provider: ImageProvider, service: ImageGenerationInterface):
        """注册图像生成服务"""
        self.services[provider] = service
    
    def set_default_provider(self, provider: ImageProvider):
        """设置默认提供商"""
        if provider in self.services:
            self.default_provider = provider
    
    def start_worker(self):
        """启动工作线程"""
        if not self.is_running:
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
    
    def stop_worker(self):
        """停止工作线程"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join()
    
    def _worker_loop(self):
        """工作线程循环"""
        while self.is_running:
            try:
                # 获取请求（带超时）
                priority, request = self.request_queue.get(timeout=1.0)
                
                # 处理请求
                result = self._process_request(request)
                
                # 缓存结果
                if result.success:
                    cache_key = self._generate_cache_key(request)
                    self.image_cache[cache_key] = result
                
                # 调用回调函数
                if request.request_id in self.result_callbacks:
                    callback = self.result_callbacks.pop(request.request_id)
                    callback(result)
                
                self.request_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                print(f"图像生成工作线程错误: {e}")
    
    def generate_async(self, request: ImageGenerationRequest, callback: callable = None) -> str:
        """异步生成图像"""
        # 检查缓存
        cache_key = self._generate_cache_key(request)
        if cache_key in self.image_cache:
            cached_result = self.image_cache[cache_key]
            if callback:
                callback(cached_result)
            return request.request_id
        
        # 添加到队列
        if callback:
            self.result_callbacks[request.request_id] = callback
        
        # 优先级队列：数字越小优先级越高
        priority = 6 - request.priority
        self.request_queue.put((priority, request))
        
        # 确保工作线程运行
        if not self.is_running:
            self.start_worker()
        
        return request.request_id
    
    def generate_sync(self, request: ImageGenerationRequest) -> ImageGenerationResult:
        """同步生成图像"""
        # 检查缓存
        cache_key = self._generate_cache_key(request)
        if cache_key in self.image_cache:
            return self.image_cache[cache_key]
        
        # 直接处理请求
        result = self._process_request(request)
        
        # 缓存结果
        if result.success:
            self.image_cache[cache_key] = result
        
        return result
    
    def _process_request(self, request: ImageGenerationRequest) -> ImageGenerationResult:
        """处理图像生成请求"""
        provider = request.provider
        
        # 尝试使用指定的提供商
        if provider in self.services:
            service = self.services[provider]
            if service.is_available():
                return service.generate_image(request)
        
        # 故障转移到默认提供商
        if self.default_provider in self.services and self.default_provider != provider:
            default_service = self.services[self.default_provider]
            if default_service.is_available():
                # 更新请求的提供商
                request.provider = self.default_provider
                result = default_service.generate_image(request)
                result.provider += " (fallback)"
                return result
        
        # 所有服务都不可用
        return ImageGenerationResult(
            request_id=request.request_id,
            success=False,
            error_message="All image generation services unavailable",
            provider="none"
        )
    
    def _generate_cache_key(self, request: ImageGenerationRequest) -> str:
        """生成缓存键"""
        key_data = {
            "prompt": request.prompt,
            "negative_prompt": request.negative_prompt,
            "style": request.style.value,
            "image_type": request.image_type.value,
            "width": request.width,
            "height": request.height,
            "steps": request.steps,
            "guidance_scale": request.guidance_scale,
            "seed": request.seed
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()
    
    def generate_npc_portrait(self, npc: NPC, style: ImageStyle = ImageStyle.ANIME) -> Optional[str]:
        """为NPC生成肖像"""
        # 检查是否已有缓存
        cache_key = f"{npc.id}_{style.value}"
        if cache_key in self.npc_portraits:
            return self.npc_portraits[cache_key]
        
        # 构建提示词
        prompt = self._build_npc_prompt(npc, style)
        
        # 创建生成请求
        request = ImageGenerationRequest(
            prompt=prompt,
            negative_prompt="ugly, deformed, blurry, low quality",
            style=style,
            image_type=ImageType.PORTRAIT,
            width=512,
            height=512,
            provider=self.default_provider,
            priority=3
        )
        
        # 同步生成
        result = self.generate_sync(request)
        
        if result.success:
            self.npc_portraits[cache_key] = result.image_path
            return result.image_path
        
        return None
    
    def _build_npc_prompt(self, npc: NPC, style: ImageStyle) -> str:
        """构建NPC的图像生成提示词"""
        # 基础描述
        gender_desc = "male" if npc.gender == Gender.MALE else "female"
        age_desc = self._get_age_description(npc.age)
        
        # 性格特征影响外观
        personality_desc = self._get_personality_description(npc.personality)
        
        # 职业影响服装
        job_desc = self._get_job_description(npc.current_job.job_type if npc.current_job else None)
        
        # 风格前缀
        style_prefix = {
            ImageStyle.ANIME: "anime style, manga style,",
            ImageStyle.REALISTIC: "photorealistic, detailed,",
            ImageStyle.CARTOON: "cartoon style, colorful,",
            ImageStyle.PIXEL_ART: "pixel art style, 8-bit,"
        }.get(style, "")
        
        # 组合提示词
        prompt_parts = [
            style_prefix,
            f"{age_desc} {gender_desc} person,",
            personality_desc,
            job_desc,
            "high quality, detailed, well-lit"
        ]
        
        prompt = " ".join(filter(None, prompt_parts))
        return prompt
    
    def _get_age_description(self, age: int) -> str:
        """根据年龄获取描述"""
        if age < 18:
            return "young teenager"
        elif age < 25:
            return "young adult"
        elif age < 40:
            return "adult"
        elif age < 60:
            return "middle-aged"
        else:
            return "elderly"
    
    def _get_personality_description(self, personality) -> str:
        """根据性格获取外观描述"""
        descriptions = []
        
        if personality.extraversion > 70:
            descriptions.append("confident expression, bright smile")
        elif personality.extraversion < 30:
            descriptions.append("shy expression, gentle look")
        
        if personality.conscientiousness > 70:
            descriptions.append("neat appearance, professional")
        
        if personality.openness > 70:
            descriptions.append("creative style, unique fashion")
        
        if personality.agreeableness > 70:
            descriptions.append("kind eyes, warm expression")
        
        if personality.neuroticism > 70:
            descriptions.append("worried expression, tense")
        
        return ", ".join(descriptions) if descriptions else "neutral expression"
    
    def _get_job_description(self, job_type) -> str:
        """根据职业获取服装描述"""
        if not job_type:
            return "casual clothing"
        
        job_clothing = {
            "worker": "work uniform, safety gear",
            "clerk": "office attire, business casual",
            "manager": "business suit, professional attire",
            "professional": "formal clothing, elegant style",
            "entrepreneur": "stylish business attire",
            "government": "formal government attire",
            "student": "casual student clothing",
            "retired": "comfortable casual clothing"
        }
        
        return job_clothing.get(job_type.value if hasattr(job_type, 'value') else str(job_type), "casual clothing")
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        status = {
            "default_provider": self.default_provider.value,
            "fallback_provider": self.fallback_provider.value,
            "worker_running": self.is_running,
            "queue_size": self.request_queue.qsize(),
            "cache_size": len(self.image_cache),
            "npc_portraits": len(self.npc_portraits),
            "services": {}
        }
        
        for provider, service in self.services.items():
            status["services"][provider.value] = {
                "available": service.is_available(),
                "name": service.get_provider_name(),
                "supported_styles": [s.value for s in service.get_supported_styles()]
            }
        
        return status
    
    def clear_cache(self):
        """清理缓存"""
        self.image_cache.clear()
        self.npc_portraits.clear()


# 全局图像生成管理器实例
image_manager = ImageGenerationManager()
