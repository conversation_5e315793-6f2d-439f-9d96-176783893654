# -*- coding: utf-8 -*-
"""
Ren'Py 专用数据库管理器
使用文件存储替代SQLite，专为Ren'Py环境优化
"""

import json
import os
import pickle
from typing import List, Optional, Dict, Any
from datetime import datetime
from .data_models import (
    World, Region, Block, Building, Organization, Policy, Relationship,
    RegionType, BuildingType, OrganizationType,
    NPC, Job, Skill, PersonalityTrait, DailyStats, Goal, Activity, NPCRelationship,
    Gender, EducationLevel, JobType, ActivityType
)


class RenPyDatabaseManager:
    """Ren'Py专用数据库管理器"""
    
    def __init__(self, data_dir="saves"):
        """初始化数据库管理器"""
        self.data_dir = data_dir
        self.ensure_data_directory()
        
        # 数据存储文件路径
        self.worlds_file = os.path.join(data_dir, "worlds.json")
        self.regions_file = os.path.join(data_dir, "regions.json")
        self.blocks_file = os.path.join(data_dir, "blocks.json")
        self.npcs_file = os.path.join(data_dir, "npcs.json")
        
        # 内存缓存
        self._worlds_cache = {}
        self._regions_cache = {}
        self._blocks_cache = {}
        self._npcs_cache = {}
        
        # 加载现有数据
        self._load_all_data()
    
    def ensure_data_directory(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def _load_all_data(self):
        """加载所有数据到内存缓存"""
        try:
            # 加载世界数据
            if os.path.exists(self.worlds_file):
                with open(self.worlds_file, 'r', encoding='utf-8') as f:
                    worlds_data = json.load(f)
                    for world_id, world_data in worlds_data.items():
                        self._worlds_cache[world_id] = self._dict_to_world(world_data)
            
            # 加载区域数据
            if os.path.exists(self.regions_file):
                with open(self.regions_file, 'r', encoding='utf-8') as f:
                    regions_data = json.load(f)
                    for region_id, region_data in regions_data.items():
                        self._regions_cache[region_id] = self._dict_to_region(region_data)
            
            # 加载区块数据
            if os.path.exists(self.blocks_file):
                with open(self.blocks_file, 'r', encoding='utf-8') as f:
                    blocks_data = json.load(f)
                    for block_id, block_data in blocks_data.items():
                        self._blocks_cache[block_id] = self._dict_to_block(block_data)
                        
        except Exception as e:
            print(f"加载数据时出错: {e}")
    
    def _save_all_data(self):
        """保存所有数据到文件"""
        try:
            # 保存世界数据
            worlds_data = {}
            for world_id, world in self._worlds_cache.items():
                worlds_data[world_id] = self._world_to_dict(world)
            with open(self.worlds_file, 'w', encoding='utf-8') as f:
                json.dump(worlds_data, f, ensure_ascii=False, indent=2)
            
            # 保存区域数据
            regions_data = {}
            for region_id, region in self._regions_cache.items():
                regions_data[region_id] = self._region_to_dict(region)
            with open(self.regions_file, 'w', encoding='utf-8') as f:
                json.dump(regions_data, f, ensure_ascii=False, indent=2)
            
            # 保存区块数据
            blocks_data = {}
            for block_id, block in self._blocks_cache.items():
                blocks_data[block_id] = self._block_to_dict(block)
            with open(self.blocks_file, 'w', encoding='utf-8') as f:
                json.dump(blocks_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存数据时出错: {e}")
    
    def save_world(self, world: World) -> bool:
        """保存世界数据"""
        try:
            self._worlds_cache[world.id] = world
            self._save_all_data()
            return True
        except Exception as e:
            print(f"保存世界失败: {e}")
            return False
    
    def load_world(self, world_id: str) -> Optional[World]:
        """加载世界数据"""
        return self._worlds_cache.get(world_id)
    
    def list_worlds(self) -> List[Dict[str, Any]]:
        """列出所有世界"""
        worlds = []
        for world_id, world in self._worlds_cache.items():
            worlds.append({
                'id': world.id,
                'name': world.name,
                'created_at': world.created_at.isoformat() if hasattr(world.created_at, 'isoformat') and world.created_at else (world.created_at if isinstance(world.created_at, str) else None),
                'total_population': world.total_population
            })
        return worlds
    
    def save_region(self, region: Region) -> bool:
        """保存区域数据"""
        try:
            self._regions_cache[region.id] = region
            self._save_all_data()
            return True
        except Exception as e:
            print(f"保存区域失败: {e}")
            return False
    
    def load_regions_by_parent(self, parent_id: Optional[str]) -> List[Region]:
        """根据父级ID加载区域"""
        regions = []
        for region in self._regions_cache.values():
            if region.parent_id == parent_id:
                regions.append(region)
        return regions
    
    def save_block(self, block: Block) -> bool:
        """保存区块数据"""
        try:
            self._blocks_cache[block.id] = block
            self._save_all_data()
            return True
        except Exception as e:
            print(f"保存区块失败: {e}")
            return False
    
    def load_blocks_by_parent(self, parent_id: str) -> List[Block]:
        """根据父级ID加载区块"""
        blocks = []
        for block in self._blocks_cache.values():
            if block.parent_id == parent_id:
                blocks.append(block)
        return blocks
    
    def _world_to_dict(self, world: World) -> Dict[str, Any]:
        """将World对象转换为字典"""
        return {
            'id': world.id,
            'name': world.name,
            'created_at': world.created_at.isoformat() if hasattr(world.created_at, 'isoformat') and world.created_at else (world.created_at if isinstance(world.created_at, str) else None),
            'total_population': world.total_population,
            'total_regions': world.total_regions,
            'total_blocks': world.total_blocks
        }
    
    def _dict_to_world(self, data: Dict[str, Any]) -> World:
        """将字典转换为World对象"""
        world = World(
            id=data['id'],
            name=data['name'],
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else None
        )
        world.total_population = data.get('total_population', 0)
        world.total_regions = data.get('total_regions', 0)
        world.total_blocks = data.get('total_blocks', 0)
        return world
    
    def _region_to_dict(self, region: Region) -> Dict[str, Any]:
        """将Region对象转换为字典"""
        return {
            'id': region.id,
            'name': region.name,
            'type': region.type.value,
            'parent_id': region.parent_id,
            'world_id': region.world_id,
            'population': region.population,
            'area': region.area,
            'description': region.description
        }
    
    def _dict_to_region(self, data: Dict[str, Any]) -> Region:
        """将字典转换为Region对象"""
        return Region(
            id=data['id'],
            name=data['name'],
            type=RegionType(data['type']),
            parent_id=data.get('parent_id'),
            world_id=data['world_id'],
            population=data.get('population', 0),
            area=data.get('area', 0.0),
            description=data.get('description', '')
        )
    
    def _block_to_dict(self, block: Block) -> Dict[str, Any]:
        """将Block对象转换为字典"""
        return {
            'id': block.id,
            'name': block.name,
            'type': block.type.value,
            'parent_id': block.parent_id,
            'world_id': block.world_id,
            'population': block.population,
            'area': block.area,
            'description': block.description
        }
    
    def _dict_to_block(self, data: Dict[str, Any]) -> Block:
        """将字典转换为Block对象"""
        return Block(
            id=data['id'],
            name=data['name'],
            type=RegionType(data['type']),
            parent_id=data['parent_id'],
            world_id=data['world_id'],
            population=data.get('population', 0),
            area=data.get('area', 0.0),
            description=data.get('description', '')
        )
    
    def save_npc(self, npc: NPC) -> bool:
        """保存NPC数据"""
        try:
            self._npcs_cache[npc.id] = npc
            # 简化版本，暂时不实现完整的NPC保存
            return True
        except Exception as e:
            print(f"保存NPC失败: {e}")
            return False
    
    def load_npcs_by_world(self, world_id: str) -> List[NPC]:
        """根据世界ID加载NPC"""
        npcs = []
        for npc in self._npcs_cache.values():
            if npc.world_id == world_id:
                npcs.append(npc)
        return npcs
