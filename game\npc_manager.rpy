# -*- coding: utf-8 -*-
# NPC管理界面

# NPC管理器主屏幕
screen npc_manager_screen():
    tag menu
    
    # 背景
    add "#1a252f"
    
    # 标题栏
    frame:
        xalign 0.5
        ypos 20
        padding (20, 10)
        background "#2c3e50"
        
        text "NPC管理系统" size 30 color "#ecf0f1" xalign 0.5
    
    # 主要内容区域
    hbox:
        xalign 0.5
        yalign 0.5
        spacing 30
        
        # 左侧：NPC列表
        frame:
            xsize 400
            ysize 600
            padding (20, 20)
            background "#2c3e50"
            
            vbox:
                spacing 15
                
                hbox:
                    text "NPC列表" size 24 color "#ecf0f1"
                    null width 20
                    textbutton "生成NPC" action Function(generate_test_npcs) text_size 14
                    textbutton "刷新" action Function(refresh_npc_list) text_size 14
                
                null height 10
                
                # 统计信息
                python:
                    if 'all_npcs' not in globals():
                        all_npcs = []
                    active_count = len([npc for npc in all_npcs if npc.is_active])
                    employed_count = len([npc for npc in all_npcs if npc.current_job])
                
                text "总数: [len(all_npcs)] | 活跃: [active_count] | 有工作: [employed_count]" size 14 color "#95a5a6"
                
                null height 10
                
                # NPC列表
                viewport:
                    scrollbars "vertical"
                    mousewheel True
                    ysize 450
                    
                    vbox:
                        spacing 5
                        
                        for npc in all_npcs:
                            python:
                                npc_bg_color = "#34495e" if npc.is_active else "#2c3e50"

                            frame:
                                padding (10, 8)
                                background "[npc_bg_color]"
                                xfill True
                                
                                hbox:
                                    spacing 10

                                    python:
                                        status_color = "#27ae60" if npc.is_active else "#95a5a6"

                                    # 状态指示器
                                    text "●" size 16 color "[status_color]"
                                    
                                    vbox:
                                        spacing 2
                                        
                                        text "[npc.name]" size 16 color "#ecf0f1"
                                        text "[npc.age]岁 [npc.gender.value]" size 12 color "#bdc3c7"
                                        
                                        if npc.current_job:
                                            text "[npc.current_job.title]" size 12 color "#3498db"
                                        else:
                                            text "无业" size 12 color "#e74c3c"
                                    
                                    null width 10
                                    
                                    # 状态条
                                    vbox:
                                        spacing 2
                                        
                                        # 精力条
                                        frame:
                                            xsize 80
                                            ysize 8
                                            background "#34495e"
                                            
                                            frame:
                                                xsize int(80 * npc.daily_stats.energy / 100)
                                                ysize 8
                                                background "#f39c12"
                                        
                                        # 幸福度条
                                        frame:
                                            xsize 80
                                            ysize 8
                                            background "#34495e"
                                            
                                            frame:
                                                xsize int(80 * npc.daily_stats.happiness / 100)
                                                ysize 8
                                                background "#27ae60"
                                    
                                    textbutton "详情" action Function(show_npc_details, npc) text_size 12
        
        # 右侧：详细信息面板
        frame:
            xsize 500
            ysize 600
            padding (20, 20)
            background "#2c3e50"
            
            if selected_npc:
                use npc_details_panel(selected_npc)
            else:
                vbox:
                    spacing 20
                    yalign 0.5
                    
                    text "选择一个NPC查看详细信息" size 20 color "#95a5a6" xalign 0.5
                    text "或者生成一些测试NPC开始体验" size 16 color "#7f8c8d" xalign 0.5
    
    # 底部控制栏
    hbox:
        xalign 0.5
        ypos 680
        spacing 20
        
        textbutton "时间推进" action Function(advance_game_time) text_size 16
        textbutton "经济报告" action Function(show_economy_report) text_size 16
        textbutton "市场分析" action Function(show_market_analysis) text_size 16
        textbutton "返回世界" action Jump("world_browser") text_size 16

# NPC详细信息面板
screen npc_details_panel(npc):
    vbox:
        spacing 15
        
        # 基本信息
        text "[npc.name] 的详细信息" size 20 color "#ecf0f1"
        
        null height 5
        
        # 基础属性
        frame:
            padding (15, 10)
            background "#34495e"
            xfill True
            
            vbox:
                spacing 8
                
                text "基础信息" size 16 color "#ecf0f1"
                text "年龄: [npc.age]岁" size 14 color "#bdc3c7"
                text "性别: [npc.gender.value]" size 14 color "#bdc3c7"
                text "教育: [npc.education_level.value]" size 14 color "#bdc3c7"
                text "婚姻: [npc.marital_status]" size 14 color "#bdc3c7"
        
        # 经济状况
        frame:
            padding (15, 10)
            background "#34495e"
            xfill True
            
            vbox:
                spacing 8
                
                text "经济状况" size 16 color "#ecf0f1"
                text "现金: ¥[npc.cash:,.0f]" size 14 color "#bdc3c7"
                text "存款: ¥[npc.bank_balance:,.0f]" size 14 color "#bdc3c7"
                text "月收入: ¥[npc.monthly_income:,.0f]" size 14 color "#bdc3c7"
                text "月支出: ¥[npc.monthly_expenses:,.0f]" size 14 color "#bdc3c7"
        
        # 工作信息
        frame:
            padding (15, 10)
            background "#34495e"
            xfill True
            
            vbox:
                spacing 8
                
                text "工作信息" size 16 color "#ecf0f1"
                if npc.current_job:
                    text "职位: [npc.current_job.title]" size 14 color "#bdc3c7"
                    text "类型: [npc.current_job.job_type.value]" size 14 color "#bdc3c7"
                    text "薪资: ¥[npc.current_job.salary:,.0f]" size 14 color "#bdc3c7"
                    text "工时: [npc.current_job.work_hours_per_day]小时/天" size 14 color "#bdc3c7"
                else:
                    text "当前无业" size 14 color "#e74c3c"
        
        # 日常状态
        frame:
            padding (15, 10)
            background "#34495e"
            xfill True
            
            vbox:
                spacing 8
                
                text "日常状态" size 16 color "#ecf0f1"
                
                # 状态条显示
                hbox:
                    spacing 10
                    
                    vbox:
                        spacing 5
                        text "精力" size 12 color "#bdc3c7"
                        text "幸福" size 12 color "#bdc3c7"
                        text "压力" size 12 color "#bdc3c7"
                        text "健康" size 12 color "#bdc3c7"
                    
                    vbox:
                        spacing 5
                        
                        # 精力条
                        hbox:
                            frame:
                                xsize 100
                                ysize 12
                                background "#2c3e50"
                                
                                frame:
                                    xsize int(100 * npc.daily_stats.energy / 100)
                                    ysize 12
                                    background "#f39c12"
                            
                            text "[npc.daily_stats.energy:.0f]" size 12 color "#bdc3c7"
                        
                        # 幸福度条
                        hbox:
                            frame:
                                xsize 100
                                ysize 12
                                background "#2c3e50"
                                
                                frame:
                                    xsize int(100 * npc.daily_stats.happiness / 100)
                                    ysize 12
                                    background "#27ae60"
                            
                            text "[npc.daily_stats.happiness:.0f]" size 12 color "#bdc3c7"
                        
                        # 压力条
                        hbox:
                            frame:
                                xsize 100
                                ysize 12
                                background "#2c3e50"
                                
                                frame:
                                    xsize int(100 * npc.daily_stats.stress / 100)
                                    ysize 12
                                    background "#e74c3c"
                            
                            text "[npc.daily_stats.stress:.0f]" size 12 color "#bdc3c7"
                        
                        # 健康条
                        hbox:
                            frame:
                                xsize 100
                                ysize 12
                                background "#2c3e50"
                                
                                frame:
                                    xsize int(100 * npc.daily_stats.health / 100)
                                    ysize 12
                                    background "#3498db"
                            
                            text "[npc.daily_stats.health:.0f]" size 12 color "#bdc3c7"

# 经济报告屏幕
screen economy_report_screen():
    modal True
    
    # 半透明背景
    add "#000000aa"
    
    # 报告面板
    frame:
        xalign 0.5
        yalign 0.5
        xsize 600
        ysize 500
        padding (30, 30)
        background "#2c3e50"
        
        vbox:
            spacing 20
            
            text "经济系统报告" size 24 color "#ecf0f1" xalign 0.5
            
            if economy_summary:
                vbox:
                    spacing 15
                    
                    text "总体统计" size 18 color "#ecf0f1"
                    text "NPC总数: [economy_summary.get('total_npcs', 0)]" size 16 color "#bdc3c7"
                    text "总现金: ¥[economy_summary.get('total_cash', 0):,.0f]" size 16 color "#bdc3c7"
                    text "月总收入: ¥[economy_summary.get('total_monthly_income', 0):,.0f]" size 16 color "#bdc3c7"
                    text "月总支出: ¥[economy_summary.get('total_monthly_expenses', 0):,.0f]" size 16 color "#bdc3c7"
                    
                    null height 10
                    
                    text "就业统计" size 18 color "#ecf0f1"
                    python:
                        employment_stats = economy_summary.get('employment_stats', {})
                    
                    for job_type, count in employment_stats.items():
                        text "[job_type]: [count]人" size 14 color "#bdc3c7"
                    
                    null height 10
                    
                    text "市场信息" size 18 color "#ecf0f1"
                    text "可用工作: [economy_summary.get('available_jobs', 0)]个" size 16 color "#bdc3c7"
                    text "交易记录: [economy_summary.get('total_transactions', 0)]笔" size 16 color "#bdc3c7"
                    text "商品种类: [economy_summary.get('products_available', 0)]种" size 16 color "#bdc3c7"
            else:
                text "暂无经济数据" size 16 color "#95a5a6" xalign 0.5
            
            null height 20
            
            textbutton "关闭" action Return() xalign 0.5 text_size 16

# 全局变量和函数
init python:
    selected_npc = None
    all_npcs = []
    economy_summary = {}
    market_analysis_data = {}
    
    def show_npc_details(npc):
        global selected_npc
        selected_npc = npc
        renpy.restart_interaction()
    
    def refresh_npc_list():
        global all_npcs
        if db_manager:
            all_npcs = db_manager.load_all_active_npcs()
        renpy.restart_interaction()
    
    def generate_test_npcs():
        global all_npcs
        if not db_manager:
            renpy.notify("数据库未初始化")
            return
        
        try:
            from core.npc_generator import NPCGenerator
            npc_gen = NPCGenerator(db_manager)
            
            # 生成5个测试NPC
            for i in range(5):
                npc = npc_gen.generate_npc()
                db_manager.save_npc(npc)
            
            refresh_npc_list()
            renpy.notify("已生成5个测试NPC")
        except Exception as e:
            renpy.notify(f"生成NPC失败: {e}")
    
    def advance_game_time():
        if not db_manager:
            renpy.notify("系统未初始化")
            return
        
        try:
            from core.time_system import TimeSystem
            from core.behavior_system import BehaviorSystem
            from core.economy_system import EconomySystem
            
            time_sys = TimeSystem(db_manager)
            behavior_sys = BehaviorSystem(db_manager)
            economy_sys = EconomySystem(db_manager)
            
            # 推进1小时
            results = time_sys.advance_time(1)
            
            # 处理NPC行为
            for npc in all_npcs:
                intentions = behavior_sys.generate_intentions(npc, time_sys.current_hour)
                plan = behavior_sys.create_action_plan(npc, intentions, time_sys.current_hour)
                behavior_sys.update_plan(npc.id, plan)
            
            # 更新经济状态
            global economy_summary
            economy_summary = economy_sys.get_economic_summary()
            
            refresh_npc_list()
            
            time_info = time_sys.get_current_time_info()
            renpy.notify(f"时间推进到: {time_info['time_string']}")
            
        except Exception as e:
            renpy.notify(f"时间推进失败: {e}")
    
    def show_economy_report():
        global economy_summary
        if not db_manager:
            renpy.notify("系统未初始化")
            return
        
        try:
            from core.economy_system import EconomySystem
            economy_sys = EconomySystem(db_manager)
            economy_summary = economy_sys.get_economic_summary()
            renpy.call_screen("economy_report_screen")
        except Exception as e:
            renpy.notify(f"获取经济报告失败: {e}")

    def show_market_analysis():
        global market_analysis_data
        if not db_manager:
            renpy.notify("系统未初始化")
            return

        try:
            from core.time_system import TimeSystem
            time_sys = TimeSystem(db_manager)
            market_analysis_data = time_sys.get_market_summary()
            renpy.call_screen("market_analysis_screen")
        except Exception as e:
            renpy.notify(f"获取市场分析失败: {e}")

# 市场分析屏幕
screen market_analysis_screen():
    modal True

    # 半透明背景
    add "#000000aa"

    # 主面板
    frame:
        xalign 0.5
        yalign 0.5
        xsize 900
        ysize 650
        padding (30, 30)
        background "#2c3e50"

        vbox:
            spacing 20

            # 标题
            text "市场分析报告" size 28 color "#ecf0f1" xalign 0.5

            # 滚动区域
            viewport:
                xsize 840
                ysize 520
                scrollbars "vertical"
                mousewheel True

                vbox:
                    spacing 15

                    # 基本信息
                    frame:
                        padding (15, 15)
                        background "#34495e"

                        vbox:
                            spacing 10
                            text "市场概况" size 20 color "#3498db"

                            if market_analysis_data:
                                $ economy = market_analysis_data.get("economy", {})
                                $ analysis = market_analysis_data.get("market_analysis", {})
                                $ events = market_analysis_data.get("events", {})

                                text "当前季节: [economy.get('current_season', '未知')]" size 16 color "#bdc3c7"
                                text "市场活跃度: [economy.get('market_activity', '未知')]" size 16 color "#bdc3c7"
                                text "今日交易量: [economy.get('daily_transactions', 0)]笔" size 16 color "#bdc3c7"
                                text "供需比: [economy.get('supply_demand_ratio', 0):.2f]" size 16 color "#bdc3c7"
                                text "平均价格变化: [economy.get('avg_price_change', 0):.1f]%" size 16 color "#bdc3c7"

                    # 类别分析
                    if market_analysis_data and "market_analysis" in market_analysis_data:
                        $ category_analysis = market_analysis_data["market_analysis"].get("category_analysis", {})

                        if category_analysis:
                            frame:
                                padding (15, 15)
                                background "#34495e"

                                vbox:
                                    spacing 10
                                    text "类别分析" size 20 color "#3498db"

                                    for category, stats in category_analysis.items():
                                        hbox:
                                            spacing 20
                                            text "[category]:" size 14 color "#e67e22" xsize 100
                                            text "平均价格: ¥[stats.get('avg_price', 0):.0f]" size 14 color "#bdc3c7" xsize 150
                                            text "供应: [stats.get('total_supply', 0):.0f]" size 14 color "#bdc3c7" xsize 100
                                            text "需求: [stats.get('total_demand', 0):.0f]" size 14 color "#bdc3c7" xsize 100
                                            text "交易: [stats.get('transactions', 0)]笔" size 14 color "#bdc3c7"

                    # 价格趋势
                    if market_analysis_data and "market_analysis" in market_analysis_data:
                        $ price_trends = market_analysis_data["market_analysis"].get("price_trends", {})

                        if price_trends:
                            frame:
                                padding (15, 15)
                                background "#34495e"

                                vbox:
                                    spacing 10
                                    text "价格趋势" size 20 color "#3498db"

                                    for product_name, trend_data in price_trends.items():
                                        hbox:
                                            spacing 20
                                            text "[product_name]:" size 14 color "#e67e22" xsize 120
                                            text "趋势: [trend_data.get('trend', '未知')]" size 14 color "#bdc3c7" xsize 80
                                            text "当前价格: ¥[trend_data.get('current_price', 0):.0f]" size 14 color "#bdc3c7" xsize 120
                                            text "变化率: [trend_data.get('change_rate', 0):.1f]%" size 14 color "#bdc3c7"

                    # 市场事件
                    if market_analysis_data and "events" in market_analysis_data:
                        $ events_data = market_analysis_data["events"]
                        $ active_events = events_data.get("active_events", [])

                        frame:
                            padding (15, 15)
                            background "#34495e"

                            vbox:
                                spacing 10
                                text "活跃市场事件" size 20 color "#3498db"

                                if active_events:
                                    for event in active_events:
                                        frame:
                                            padding (10, 10)
                                            background "#2c3e50"

                                            vbox:
                                                spacing 5
                                                text "[event.get('name', '未知事件')]" size 16 color "#e74c3c"
                                                text "类型: [event.get('type', '未知')] | 严重程度: [event.get('severity', '未知')]" size 12 color "#95a5a6"
                                                text "剩余天数: [event.get('duration_remaining', 0)]天" size 12 color "#95a5a6"
                                                text "影响: [event.get('impact', '无')]" size 12 color "#f39c12"
                                                text "受影响类别: [', '.join(event.get('affected_categories', []))]" size 12 color "#95a5a6"
                                else:
                                    text "当前没有活跃的市场事件" size 14 color "#95a5a6"

            # 关闭按钮
            textbutton "关闭" action Return() text_size 18 xalign 0.5

# NPC管理器入口标签
label npc_manager:
    call init_system
    
    python:
        refresh_npc_list()
    
    call screen npc_manager_screen
    
    return
