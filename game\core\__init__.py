# -*- coding: utf-8 -*-
"""
游戏核心模块
包含数据模型、数据库管理、世界生成等核心功能
"""

from .data_models import (
    World, Region, Block, Building, Organization, Policy, Relationship,
    RegionType, BuildingType, OrganizationType,
    NPC, Job, Skill, PersonalityTrait, DailyStats, Goal, Activity, NPCRelationship,
    Gender, EducationLevel, JobType, ActivityType,
    generate_uuid, get_region_level, get_parent_id,
    validate_hierarchical_id, validate_region_hierarchy
)

# 根据环境选择数据库管理器
try:
    import sqlite3
    # 尝试导入SQLite数据库管理器
    try:
        from .database import DatabaseManager
        print("使用SQLite数据库管理器")
    except ImportError:
        # 如果SQLite数据库管理器导入失败，使用文件数据库管理器
        from .renpy_database import RenPyDatabaseManager as DatabaseManager
        print("使用Ren'Py文件数据库管理器（SQLite模块可用但数据库管理器导入失败）")
except ImportError:
    # sqlite3模块不可用，使用文件数据库管理器
    from .renpy_database import RenPyDatabaseManager as DatabaseManager
    print("使用Ren'Py文件数据库管理器（sqlite3模块不可用）")

from .world_generator import WorldGenerator

from .npc_generator import NPCGenerator

from .time_system import TimeSystem

from .behavior_system import BehaviorSystem

from .economy_system import EconomySystem

__version__ = "1.0.0"
__author__ = "Game Development Team"

# 导出主要类和函数
__all__ = [
    # 数据模型
    'World', 'Region', 'Block', 'Building', 'Organization', 'Policy', 'Relationship',
    'RegionType', 'BuildingType', 'OrganizationType',
    'NPC', 'Job', 'Skill', 'PersonalityTrait', 'DailyStats', 'Goal', 'Activity', 'NPCRelationship',
    'Gender', 'EducationLevel', 'JobType', 'ActivityType',

    # 工具函数
    'generate_uuid', 'get_region_level', 'get_parent_id',
    'validate_hierarchical_id', 'validate_region_hierarchy',

    # 管理器
    'DatabaseManager', 'WorldGenerator', 'NPCGenerator', 'TimeSystem', 'BehaviorSystem', 'EconomySystem'
]
