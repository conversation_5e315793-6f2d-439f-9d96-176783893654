#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
最终验证脚本 - 确认所有修复都已生效
"""

import sys
import os

# 添加game目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))

def test_normal_environment():
    """测试正常环境（有sqlite3）"""
    print("=== 测试正常环境 ===")
    
    try:
        from core import DatabaseManager, WorldGenerator, World
        print("✅ 核心模块导入成功")
        
        db_manager = DatabaseManager("test_final.db")
        print("✅ 数据库管理器初始化成功")
        
        world_generator = WorldGenerator(db_manager)
        print("✅ 世界生成器初始化成功")
        
        # 清理测试文件
        if os.path.exists("test_final.db"):
            os.remove("test_final.db")
        
        return True
        
    except Exception as e:
        print(f"❌ 正常环境测试失败: {e}")
        return False

def test_renpy_environment():
    """测试Ren'Py环境（无sqlite3）"""
    print("\n=== 测试Ren'Py环境 ===")
    
    # 清理模块缓存
    modules_to_clear = [
        'core', 'core.database', 'core.renpy_database', 
        'core.data_models', 'core.world_generator'
    ]
    for module in modules_to_clear:
        if module in sys.modules:
            del sys.modules[module]
    
    # 模拟Ren'Py环境
    import builtins
    original_import = builtins.__import__
    
    def mock_import(name, *args, **kwargs):
        if name == 'sqlite3':
            raise ImportError("No module named 'sqlite3'")
        return original_import(name, *args, **kwargs)
    
    builtins.__import__ = mock_import
    
    try:
        from core import DatabaseManager, WorldGenerator, World
        print("✅ 核心模块导入成功（使用文件数据库）")
        
        db_manager = DatabaseManager("test_renpy_saves")
        print("✅ 文件数据库管理器初始化成功")
        
        world_generator = WorldGenerator(db_manager)
        print("✅ 世界生成器初始化成功")
        
        # 测试基本功能
        world = world_generator.generate_world(
            world_name="验证测试世界",
            num_countries=1,
            provinces_per_country=(1, 1),
            cities_per_province=(1, 1),
            districts_per_city=(1, 1),
            villages_per_city=(1, 1)
        )
        print(f"✅ 世界生成成功: {world.name}")
        
        # 测试数据保存
        saved = db_manager.save_world(world)
        if saved:
            print("✅ 数据保存成功")
        else:
            print("❌ 数据保存失败")
            return False
        
        # 测试数据加载
        loaded_world = db_manager.load_world(world.id)
        if loaded_world and loaded_world.name == world.name:
            print("✅ 数据加载成功")
        else:
            print("❌ 数据加载失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Ren'Py环境测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 恢复原始导入函数
        builtins.__import__ = original_import
        
        # 清理测试数据
        import shutil
        if os.path.exists("test_renpy_saves"):
            try:
                shutil.rmtree("test_renpy_saves")
            except:
                pass

def main():
    """主验证函数"""
    print("🔍 最终验证 - 确认所有修复生效")
    print("=" * 50)
    
    # 测试正常环境
    normal_ok = test_normal_environment()
    
    # 测试Ren'Py环境
    renpy_ok = test_renpy_environment()
    
    print("\n" + "=" * 50)
    print("最终验证结果:")
    print(f"  正常环境: {'✅ 通过' if normal_ok else '❌ 失败'}")
    print(f"  Ren'Py环境: {'✅ 通过' if renpy_ok else '❌ 失败'}")
    
    if normal_ok and renpy_ok:
        print("\n🎉 所有验证通过！修复完全成功！")
        print("\n✨ 系统状态:")
        print("  - SQLite环境：完全兼容")
        print("  - Ren'Py环境：完全兼容")
        print("  - 自动环境检测：正常工作")
        print("  - 数据持久化：正常工作")
        print("  - 世界生成：正常工作")
        
        print("\n🚀 现在可以启动游戏了！")
        print("方法1: 双击 start_game.bat")
        print("方法2: 使用Ren'Py启动器")
        print("方法3: 命令行运行 renpy.exe .")
        
        print("\n🎮 启动后应该看到:")
        print("  - '欢迎来到最小化测试版本！'")
        print("  - '✅ 核心系统加载成功！'")
        print("  - '✅ 系统初始化成功！'")
        print("  - 而不是任何错误信息")
        
        return True
    else:
        print("\n❌ 部分验证失败")
        print("请检查错误信息并重新运行修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
