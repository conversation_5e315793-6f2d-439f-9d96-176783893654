# -*- coding: utf-8 -*-
"""
多媒体内容管理系统
统一管理音频、图像、视频等多媒体资源
"""

import os
import json
import shutil
import threading
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import sqlite3

from .data_models import NPC, generate_uuid
from .tts_system import TTSManager, TTSRequest, VoiceProfile
from .image_generation import ImageGenerationManager, ImageGenerationRequest, ImageStyle, ImageType


class MediaType(Enum):
    """媒体类型"""
    AUDIO = "audio"
    IMAGE = "image"
    VIDEO = "video"
    TEXT = "text"


class MediaCategory(Enum):
    """媒体分类"""
    NPC_VOICE = "npc_voice"          # NPC语音
    NPC_PORTRAIT = "npc_portrait"    # NPC肖像
    SCENE_IMAGE = "scene_image"      # 场景图片
    BACKGROUND = "background"        # 背景图
    NEWS_AUDIO = "news_audio"        # 新闻音频
    DIALOGUE = "dialogue"            # 对话音频
    AMBIENT = "ambient"              # 环境音效
    CUTSCENE = "cutscene"           # 过场动画


@dataclass
class MediaAsset:
    """媒体资源"""
    asset_id: str
    name: str
    media_type: MediaType
    category: MediaCategory
    file_path: str
    thumbnail_path: str = ""
    duration: float = 0.0  # 音频/视频时长
    file_size: int = 0
    width: int = 0  # 图像/视频宽度
    height: int = 0  # 图像/视频高度
    created_at: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()


@dataclass
class MediaGenerationTask:
    """媒体生成任务"""
    task_id: str
    media_type: MediaType
    category: MediaCategory
    target_id: str  # NPC ID或其他目标ID
    parameters: Dict[str, Any] = field(default_factory=dict)
    priority: int = 1
    status: str = "pending"  # pending, processing, completed, failed
    progress: float = 0.0
    result_asset_id: str = ""
    error_message: str = ""
    created_at: str = ""
    completed_at: str = ""
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()


class MultimediaManager:
    """多媒体管理器"""
    
    def __init__(self, media_directory: str = "media"):
        self.media_directory = media_directory
        self.tts_manager = TTSManager(os.path.join(media_directory, "audio"))
        self.image_manager = ImageGenerationManager(os.path.join(media_directory, "images"))
        
        # 创建目录结构
        self.audio_dir = os.path.join(media_directory, "audio")
        self.image_dir = os.path.join(media_directory, "images")
        self.video_dir = os.path.join(media_directory, "videos")
        self.cache_dir = os.path.join(media_directory, "cache")
        
        for directory in [self.audio_dir, self.image_dir, self.video_dir, self.cache_dir]:
            os.makedirs(directory, exist_ok=True)
        
        # 媒体资源数据库
        self.db_path = os.path.join(media_directory, "media_assets.db")
        self._init_database()
        
        # 任务队列和处理
        self.generation_tasks: Dict[str, MediaGenerationTask] = {}
        self.task_queue: List[MediaGenerationTask] = []
        self.worker_thread = None
        self.is_running = False
        
        # 缓存
        self.asset_cache: Dict[str, MediaAsset] = {}
        
        # 启动服务
        self.tts_manager.start_worker()
        self.image_manager.start_worker()
    
    def _init_database(self):
        """初始化媒体资源数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS media_assets (
                asset_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                media_type TEXT NOT NULL,
                category TEXT NOT NULL,
                file_path TEXT NOT NULL,
                thumbnail_path TEXT,
                duration REAL,
                file_size INTEGER,
                width INTEGER,
                height INTEGER,
                created_at TEXT,
                metadata TEXT,
                tags TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS generation_tasks (
                task_id TEXT PRIMARY KEY,
                media_type TEXT NOT NULL,
                category TEXT NOT NULL,
                target_id TEXT NOT NULL,
                parameters TEXT,
                priority INTEGER,
                status TEXT,
                progress REAL,
                result_asset_id TEXT,
                error_message TEXT,
                created_at TEXT,
                completed_at TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def start_worker(self):
        """启动工作线程"""
        if not self.is_running:
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
    
    def stop_worker(self):
        """停止工作线程"""
        self.is_running = False
        self.tts_manager.stop_worker()
        self.image_manager.stop_worker()
        if self.worker_thread:
            self.worker_thread.join()
    
    def _worker_loop(self):
        """工作线程循环"""
        while self.is_running:
            try:
                # 处理待处理的任务
                if self.task_queue:
                    # 按优先级排序
                    self.task_queue.sort(key=lambda x: x.priority, reverse=True)
                    task = self.task_queue.pop(0)
                    
                    # 处理任务
                    self._process_generation_task(task)
                
                # 短暂休眠
                threading.Event().wait(0.1)
                
            except Exception as e:
                print(f"多媒体工作线程错误: {e}")
    
    def _process_generation_task(self, task: MediaGenerationTask):
        """处理生成任务"""
        try:
            task.status = "processing"
            self._save_task(task)
            
            if task.media_type == MediaType.AUDIO:
                result = self._process_audio_task(task)
            elif task.media_type == MediaType.IMAGE:
                result = self._process_image_task(task)
            elif task.media_type == MediaType.VIDEO:
                result = self._process_video_task(task)
            else:
                raise ValueError(f"不支持的媒体类型: {task.media_type}")
            
            if result:
                task.status = "completed"
                task.progress = 1.0
                task.result_asset_id = result.asset_id
                task.completed_at = datetime.now().isoformat()
                
                # 保存资源到数据库
                self._save_asset(result)
            else:
                task.status = "failed"
                task.error_message = "生成失败"
            
        except Exception as e:
            task.status = "failed"
            task.error_message = str(e)
        
        finally:
            self._save_task(task)
    
    def _process_audio_task(self, task: MediaGenerationTask) -> Optional[MediaAsset]:
        """处理音频生成任务"""
        try:
            text = task.parameters.get("text", "")
            voice_profile_dict = task.parameters.get("voice_profile")

            if not text or not voice_profile_dict:
                return None

            # 从字典重建VoiceProfile对象
            from .tts_system import VoiceProfile, VoiceGender, VoiceStyle, TTSProvider
            voice_profile = VoiceProfile(
                voice_id=voice_profile_dict["voice_id"],
                name=voice_profile_dict["name"],
                gender=VoiceGender(voice_profile_dict["gender"]),
                language=voice_profile_dict["language"],
                style=VoiceStyle(voice_profile_dict["style"]),
                speed=voice_profile_dict["speed"],
                pitch=voice_profile_dict["pitch"],
                volume=voice_profile_dict["volume"],
                provider=TTSProvider(voice_profile_dict["provider"])
            )
            
            # 创建TTS请求
            tts_request = TTSRequest(
                text=text,
                voice_profile=voice_profile,
                priority=task.priority
            )
            
            # 同步生成音频
            tts_result = self.tts_manager.synthesize_sync(tts_request)
            
            if tts_result.success:
                # 创建媒体资源
                asset = MediaAsset(
                    asset_id=generate_uuid(),
                    name=f"Audio_{task.target_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    media_type=MediaType.AUDIO,
                    category=task.category,
                    file_path=tts_result.audio_file_path,
                    duration=tts_result.duration,
                    file_size=tts_result.file_size,
                    metadata={
                        "text": text,
                        "voice_profile": voice_profile.to_dict(),
                        "provider": tts_result.provider,
                        "generation_time": tts_result.generation_time
                    },
                    tags=[task.target_id, "tts", voice_profile.style.value]
                )
                
                return asset
            
        except Exception as e:
            print(f"音频任务处理失败: {e}")
        
        return None
    
    def _process_image_task(self, task: MediaGenerationTask) -> Optional[MediaAsset]:
        """处理图像生成任务"""
        try:
            prompt = task.parameters.get("prompt", "")
            style_str = task.parameters.get("style", "anime")
            image_type_str = task.parameters.get("image_type", "portrait")

            if not prompt:
                return None

            # 从字符串重建枚举对象
            from .image_generation import ImageStyle, ImageType
            style = ImageStyle(style_str)
            image_type = ImageType(image_type_str)
            
            # 创建图像生成请求
            image_request = ImageGenerationRequest(
                prompt=prompt,
                style=style,
                image_type=image_type,
                priority=task.priority
            )
            
            # 同步生成图像
            image_result = self.image_manager.generate_sync(image_request)
            
            if image_result.success:
                # 创建媒体资源
                asset = MediaAsset(
                    asset_id=generate_uuid(),
                    name=f"Image_{task.target_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    media_type=MediaType.IMAGE,
                    category=task.category,
                    file_path=image_result.image_path,
                    thumbnail_path=image_result.thumbnail_path,
                    file_size=image_result.file_size,
                    width=image_result.width,
                    height=image_result.height,
                    metadata={
                        "prompt": prompt,
                        "style": style.value,
                        "image_type": image_type.value,
                        "provider": image_result.provider,
                        "generation_time": image_result.generation_time
                    },
                    tags=[task.target_id, "generated", style.value]
                )
                
                return asset
            
        except Exception as e:
            print(f"图像任务处理失败: {e}")
        
        return None
    
    def _process_video_task(self, task: MediaGenerationTask) -> Optional[MediaAsset]:
        """处理视频生成任务（暂未实现）"""
        # 这里可以集成视频生成模型
        return None
    
    def generate_npc_voice(self, npc: NPC, text: str, priority: int = 1) -> str:
        """为NPC生成语音"""
        voice_profile = self.tts_manager.get_npc_voice_profile(npc)
        
        task = MediaGenerationTask(
            task_id=generate_uuid(),
            media_type=MediaType.AUDIO,
            category=MediaCategory.NPC_VOICE,
            target_id=npc.id,
            parameters={
                "text": text,
                "voice_profile": voice_profile.to_dict()  # 转换为字典
            },
            priority=priority
        )
        
        self.generation_tasks[task.task_id] = task
        self.task_queue.append(task)
        self._save_task(task)
        
        # 确保工作线程运行
        if not self.is_running:
            self.start_worker()
        
        return task.task_id
    
    def generate_npc_portrait(self, npc: NPC, style: ImageStyle = ImageStyle.ANIME, priority: int = 1) -> str:
        """为NPC生成肖像"""
        # 构建提示词
        prompt = self.image_manager._build_npc_prompt(npc, style)
        
        task = MediaGenerationTask(
            task_id=generate_uuid(),
            media_type=MediaType.IMAGE,
            category=MediaCategory.NPC_PORTRAIT,
            target_id=npc.id,
            parameters={
                "prompt": prompt,
                "style": style.value,  # 转换为字符串
                "image_type": ImageType.PORTRAIT.value  # 转换为字符串
            },
            priority=priority
        )
        
        self.generation_tasks[task.task_id] = task
        self.task_queue.append(task)
        self._save_task(task)
        
        # 确保工作线程运行
        if not self.is_running:
            self.start_worker()
        
        return task.task_id
    
    def get_task_status(self, task_id: str) -> Optional[MediaGenerationTask]:
        """获取任务状态"""
        if task_id in self.generation_tasks:
            return self.generation_tasks[task_id]
        
        # 从数据库加载
        return self._load_task(task_id)
    
    def get_asset(self, asset_id: str) -> Optional[MediaAsset]:
        """获取媒体资源"""
        if asset_id in self.asset_cache:
            return self.asset_cache[asset_id]
        
        # 从数据库加载
        asset = self._load_asset(asset_id)
        if asset:
            self.asset_cache[asset_id] = asset
        
        return asset
    
    def get_npc_assets(self, npc_id: str, media_type: Optional[MediaType] = None) -> List[MediaAsset]:
        """获取NPC的媒体资源"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if media_type:
            cursor.execute(
                "SELECT * FROM media_assets WHERE tags LIKE ? AND media_type = ? ORDER BY created_at DESC",
                (f"%{npc_id}%", media_type.value)
            )
        else:
            cursor.execute(
                "SELECT * FROM media_assets WHERE tags LIKE ? ORDER BY created_at DESC",
                (f"%{npc_id}%",)
            )
        
        assets = []
        for row in cursor.fetchall():
            asset = self._row_to_asset(row)
            assets.append(asset)
        
        conn.close()
        return assets
    
    def _save_asset(self, asset: MediaAsset):
        """保存媒体资源到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO media_assets 
            (asset_id, name, media_type, category, file_path, thumbnail_path, 
             duration, file_size, width, height, created_at, metadata, tags)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            asset.asset_id, asset.name, asset.media_type.value, asset.category.value,
            asset.file_path, asset.thumbnail_path, asset.duration, asset.file_size,
            asset.width, asset.height, asset.created_at,
            json.dumps(asset.metadata), json.dumps(asset.tags)
        ))
        
        conn.commit()
        conn.close()
        
        # 更新缓存
        self.asset_cache[asset.asset_id] = asset
    
    def _load_asset(self, asset_id: str) -> Optional[MediaAsset]:
        """从数据库加载媒体资源"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM media_assets WHERE asset_id = ?", (asset_id,))
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return self._row_to_asset(row)
        
        return None
    
    def _row_to_asset(self, row) -> MediaAsset:
        """将数据库行转换为MediaAsset对象"""
        return MediaAsset(
            asset_id=row[0],
            name=row[1],
            media_type=MediaType(row[2]),
            category=MediaCategory(row[3]),
            file_path=row[4],
            thumbnail_path=row[5] or "",
            duration=row[6] or 0.0,
            file_size=row[7] or 0,
            width=row[8] or 0,
            height=row[9] or 0,
            created_at=row[10],
            metadata=json.loads(row[11]) if row[11] else {},
            tags=json.loads(row[12]) if row[12] else []
        )
    
    def _save_task(self, task: MediaGenerationTask):
        """保存任务到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO generation_tasks 
            (task_id, media_type, category, target_id, parameters, priority, 
             status, progress, result_asset_id, error_message, created_at, completed_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            task.task_id, task.media_type.value, task.category.value, task.target_id,
            json.dumps(task.parameters), task.priority, task.status, task.progress,
            task.result_asset_id, task.error_message, task.created_at, task.completed_at
        ))
        
        conn.commit()
        conn.close()
    
    def _load_task(self, task_id: str) -> Optional[MediaGenerationTask]:
        """从数据库加载任务"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM generation_tasks WHERE task_id = ?", (task_id,))
        row = cursor.fetchone()
        conn.close()
        
        if row:
            return MediaGenerationTask(
                task_id=row[0],
                media_type=MediaType(row[1]),
                category=MediaCategory(row[2]),
                target_id=row[3],
                parameters=json.loads(row[4]) if row[4] else {},
                priority=row[5],
                status=row[6],
                progress=row[7],
                result_asset_id=row[8] or "",
                error_message=row[9] or "",
                created_at=row[10],
                completed_at=row[11] or ""
            )
        
        return None
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "tts_status": self.tts_manager.get_service_status(),
            "image_status": self.image_manager.get_service_status(),
            "worker_running": self.is_running,
            "pending_tasks": len(self.task_queue),
            "total_tasks": len(self.generation_tasks),
            "cached_assets": len(self.asset_cache)
        }


# 全局多媒体管理器实例
multimedia_manager = MultimediaManager()
