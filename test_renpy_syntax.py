#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Ren'Py脚本语法
检查脚本文件是否有语法错误
"""

import os
import sys
import subprocess

def test_renpy_syntax():
    """测试Ren'Py脚本语法"""
    print("检查Ren'Py脚本语法...")
    
    # 检查主要脚本文件
    script_files = [
        "game/script.rpy",
        "game/world_browser.rpy", 
        "game/npc_manager.rpy",
        "game/options.rpy",
        "game/gui.rpy",
        "game/screens.rpy"
    ]
    
    syntax_errors = []
    
    for script_file in script_files:
        if os.path.exists(script_file):
            print(f"检查 {script_file}...")
            
            # 简单的语法检查 - 查找常见错误模式
            try:
                with open(script_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                    for i, line in enumerate(lines, 1):
                        # 检查textbutton的size属性错误
                        if 'textbutton' in line and ' size ' in line and 'text_size' not in line:
                            syntax_errors.append(f"{script_file}:{i} - 应该使用 text_size 而不是 size")

                        # 检查条件表达式在属性中的使用（但排除python块内的代码）
                        if ('background' in line or 'color' in line) and ' if ' in line and ' else ' in line:
                            # 检查这行是否在python块内，或者是注释，或者已经是变量赋值
                            if (not line.strip().startswith('python:') and
                                not line.strip().startswith('#') and
                                not '=' in line and
                                ('background "' in line or 'color "' in line)):
                                syntax_errors.append(f"{script_file}:{i} - 属性中的条件表达式应该先用python计算")

                        # 检查引号匹配
                        if line.strip().startswith('textbutton') and line.count('"') % 2 != 0:
                            syntax_errors.append(f"{script_file}:{i} - 引号不匹配")

                        # 检查常见的缩进错误
                        if line.strip().startswith('if ') and not line.strip().endswith(':'):
                            if 'if ' in line and ' else ' not in line:
                                syntax_errors.append(f"{script_file}:{i} - if语句应该以冒号结尾")

                        # 检查frame语句的子元素
                        if line.strip().startswith('frame:') and i < len(lines):
                            next_lines = lines[i:i+5]  # 检查接下来的5行
                            for j, next_line in enumerate(next_lines):
                                if next_line.strip().startswith('if ') and not next_line.strip().startswith('if '):
                                    # 这是一个简化的检查，实际情况更复杂
                                    pass
                            
            except Exception as e:
                syntax_errors.append(f"{script_file} - 读取文件失败: {e}")
        else:
            print(f"  文件不存在: {script_file}")
    
    if syntax_errors:
        print("\n❌ 发现语法错误:")
        for error in syntax_errors:
            print(f"  {error}")
        return False
    else:
        print("\n✅ 所有脚本文件语法检查通过!")
        return True

def check_file_structure():
    """检查文件结构"""
    print("\n检查项目文件结构...")
    
    required_files = [
        "game/script.rpy",
        "game/options.rpy", 
        "game/gui.rpy",
        "game/screens.rpy",
        "game/world_browser.rpy",
        "game/npc_manager.rpy"
    ]
    
    required_dirs = [
        "game/core",
        "game/saves",
        "game/images",
        "game/audio"
    ]
    
    missing_files = []
    missing_dirs = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
        else:
            print(f"✓ {dir_path}/")
    
    if missing_files or missing_dirs:
        print("\n⚠️ 缺少文件或目录:")
        for item in missing_files + missing_dirs:
            print(f"  - {item}")
        return False
    else:
        print("\n✅ 项目文件结构完整!")
        return True

def check_core_modules():
    """检查核心模块"""
    print("\n检查核心模块...")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'game'))
        
        # 测试导入核心模块
        from core import (
            DatabaseManager, WorldGenerator, NPCGenerator,
            TimeSystem, BehaviorSystem, EconomySystem
        )
        
        print("✓ 核心模块导入成功")
        
        # 测试基本初始化
        db_manager = DatabaseManager("test_syntax.db")
        print("✓ 数据库管理器初始化成功")
        
        world_generator = WorldGenerator(db_manager)
        print("✓ 世界生成器初始化成功")
        
        npc_generator = NPCGenerator(db_manager)
        print("✓ NPC生成器初始化成功")
        
        time_system = TimeSystem(db_manager)
        print("✓ 时间系统初始化成功")
        
        behavior_system = BehaviorSystem(db_manager)
        print("✓ 行为系统初始化成功")
        
        economy_system = EconomySystem(db_manager)
        print("✓ 经济系统初始化成功")
        
        print("\n✅ 所有核心模块工作正常!")
        return True
        
    except Exception as e:
        print(f"\n❌ 核心模块检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=== Ren'Py项目语法和结构检查 ===\n")
    
    # 检查文件结构
    structure_ok = check_file_structure()
    
    # 检查脚本语法
    syntax_ok = test_renpy_syntax()
    
    # 检查核心模块
    modules_ok = check_core_modules()
    
    print("\n" + "="*50)
    print("检查结果总结:")
    print(f"  文件结构: {'✅ 通过' if structure_ok else '❌ 失败'}")
    print(f"  脚本语法: {'✅ 通过' if syntax_ok else '❌ 失败'}")
    print(f"  核心模块: {'✅ 通过' if modules_ok else '❌ 失败'}")
    
    if structure_ok and syntax_ok and modules_ok:
        print("\n🎉 项目检查全部通过！可以启动Ren'Py游戏了。")
        print("\n启动建议:")
        print("1. 使用Ren'Py启动器打开项目目录")
        print("2. 或者运行: renpy.exe .")
        print("3. 游戏启动后可以:")
        print("   - 浏览世界地图")
        print("   - 进入NPC管理系统")
        print("   - 生成测试NPC")
        print("   - 体验时间推进功能")
        return True
    else:
        print("\n❌ 项目检查发现问题，请修复后再启动游戏。")
        return False

if __name__ == "__main__":
    main()
