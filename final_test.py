#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本
验证所有修复是否正确
"""

import os
import sys
import subprocess

def run_test(test_name, command):
    """运行测试并返回结果"""
    print(f"\n=== {test_name} ===")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, cwd=os.getcwd())
        if result.returncode == 0:
            print("✅ 通过")
            return True
        else:
            print("❌ 失败")
            print(f"错误输出: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🧪 最终测试 - 验证所有修复")
    print("=" * 50)
    
    tests = [
        ("Ren'Py语法检查", "python test_renpy_syntax.py"),
        ("系统诊断", "python diagnose_system.py"),
        ("Ren'Py导入测试", "python test_renpy_import.py")
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, command in tests:
        if run_test(test_name, command):
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n✅ 系统状态:")
        print("  - 文件结构完整")
        print("  - 脚本语法正确")
        print("  - 核心模块正常")
        print("  - 导入环境正常")
        
        print("\n🚀 启动建议:")
        print("  1. 双击运行 start_game.bat")
        print("  2. 或使用Ren'Py启动器选择项目目录")
        print("  3. 如果仍有问题，查看QUICK_START.md的故障排除部分")
        
    else:
        print("❌ 部分测试失败")
        print("请检查失败的测试输出，并参考故障排除指南")
    
    print("\n📚 相关文档:")
    print("  - QUICK_START.md - 快速启动指南")
    print("  - RENPY_SYNTAX_GUIDE.md - 语法修复指南")
    print("  - DEVELOPMENT_GUIDE.md - 完整开发文档")

if __name__ == "__main__":
    main()
